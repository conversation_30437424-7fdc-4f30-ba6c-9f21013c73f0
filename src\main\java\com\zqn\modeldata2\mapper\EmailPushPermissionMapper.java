package com.zqn.modeldata2.mapper;

import com.zqn.modeldata2.entity.email.EmailPermissionQuery;
import com.zqn.modeldata2.entity.email.EmailPushPermission;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 邮件推送权限配置Mapper接口
 * @date 2025/01/22 10:00
 */
@Mapper
public interface EmailPushPermissionMapper {

    /**
     * 根据条件查询邮件推送权限列表
     *
     * @param query 查询条件
     * @return 权限列表
     */
    List<EmailPushPermission> selectByCondition(EmailPermissionQuery query);

    /**
     * 根据工号查询邮件推送权限
     *
     * @param userNo 工号
     * @return 权限信息
     */
    EmailPushPermission selectByUserNo(@Param("userNo") String userNo);

    /**
     * 根据邮件类型查询有权限的用户列表
     *
     * @param emailType 邮件类型
     * @return 权限列表
     */
    List<EmailPushPermission> selectByEmailType(@Param("emailType") String emailType);

    /**
     * 根据邮件类型和部门查询有权限的用户列表
     *
     * @param emailType 邮件类型
     * @param deptName  部门名称
     * @return 权限列表
     */
    List<EmailPushPermission> selectByEmailTypeAndDept(@Param("emailType") String emailType, 
                                                       @Param("deptName") String deptName);

    /**
     * 插入邮件推送权限
     *
     * @param permission 权限信息
     * @return 影响行数
     */
    int insert(EmailPushPermission permission);

    /**
     * 根据工号更新邮件推送权限
     *
     * @param permission 权限信息
     * @return 影响行数
     */
    int updateByUserNo(EmailPushPermission permission);

    /**
     * 根据工号删除邮件推送权限
     *
     * @param userNo 工号
     * @return 影响行数
     */
    int deleteByUserNo(@Param("userNo") String userNo);

    /**
     * 批量插入邮件推送权限
     *
     * @param permissions 权限列表
     * @return 影响行数
     */
    int batchInsert(@Param("permissions") List<EmailPushPermission> permissions);

    /**
     * 批量更新邮件推送权限
     *
     * @param permissions 权限列表
     * @return 影响行数
     */
    int batchUpdate(@Param("permissions") List<EmailPushPermission> permissions);

    /**
     * 根据用户工号列表查询权限信息
     *
     * @param userNos 工号列表
     * @return 权限列表
     */
    List<EmailPushPermission> selectByUserNos(@Param("userNos") List<String> userNos);

    /**
     * 查询所有部门列表
     *
     * @return 部门列表
     */
    List<String> selectAllDepts();

    /**
     * 统计权限配置数量
     *
     * @param query 查询条件
     * @return 数量
     */
    int countByCondition(EmailPermissionQuery query);
}
