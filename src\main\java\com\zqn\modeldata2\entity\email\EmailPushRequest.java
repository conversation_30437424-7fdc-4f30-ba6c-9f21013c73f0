package com.zqn.modeldata2.entity.email;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 邮件推送请求DTO
 * @date 2025/01/22 10:00
 */
@Data
public class EmailPushRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 邮件类型 (QUOTE_V1, QUOTE_V2, QUOTE_V3, QUOTE_V4, QUOTE_V5, ESTIMATE_Z, ESTIMATE_ZZ, P_VERSION, UPDATE_NOTIFY)
     */
    @NotBlank(message = "邮件类型不能为空")
    private String emailType;

    /**
     * 业务ID（如订单号、报价单号等）
     */
    @NotBlank(message = "业务ID不能为空")
    private String businessId;

    /**
     * 业务类型
     */
    private String businessType;

    /**
     * 指定收件人工号列表（可选，如果不指定则根据权限配置自动获取）
     */
    private List<String> userNos;

    /**
     * 指定收件人邮箱列表（可选）
     */
    private List<String> emails;

    /**
     * 模板变量（用于替换邮件模板中的变量）
     */
    private Map<String, Object> templateVariables;

    /**
     * 自定义邮件主题（可选，如果不指定则使用模板主题）
     */
    private String customSubject;

    /**
     * 自定义邮件内容（可选，如果不指定则使用模板内容）
     */
    private String customContent;

    /**
     * 附件信息列表
     */
    private List<EmailAttachment> attachments;

    /**
     * 是否立即发送（true-立即发送，false-加入队列）
     */
    private Boolean immediate = false;

    /**
     * 发送人
     */
    private String sendUser;

    /**
     * 附件信息内部类
     */
    @Data
    public static class EmailAttachment implements Serializable {
        
        private static final long serialVersionUID = 1L;

        /**
         * 附件名称
         */
        private String fileName;

        /**
         * 附件路径或URL
         */
        private String filePath;

        /**
         * 附件类型
         */
        private String fileType;

        /**
         * 附件大小（字节）
         */
        private Long fileSize;

        /**
         * 附件内容（Base64编码，可选）
         */
        private String fileContent;
    }
}
