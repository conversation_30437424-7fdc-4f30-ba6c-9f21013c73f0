package com.zqn.modeldata2.controller;

import com.github.pagehelper.PageInfo;
import com.zqn.modeldata2.common.R;
import com.zqn.modeldata2.entity.email.EmailTemplate;
import com.zqn.modeldata2.service.EmailTemplateService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 邮件模板Controller
 * @date 2025/01/22 10:00
 */
@RestController
@RequestMapping("/emailTemplate")
@Validated
public class EmailTemplateController {

    @Resource
    private EmailTemplateService emailTemplateService;

    /**
     * 分页查询邮件模板列表
     *
     * @param pageNo       页码
     * @param pageSize     页大小
     * @param emailType    邮件类型
     * @param language     语言
     * @param status       状态
     * @param templateName 模板名称
     * @return 分页结果
     */
    @GetMapping("/query")
    public R<PageInfo<EmailTemplate>> queryTemplateList(
            @RequestParam(value = "pageNo", defaultValue = "1") int pageNo,
            @RequestParam(value = "pageSize", defaultValue = "10") int pageSize,
            @RequestParam(value = "emailType", required = false) String emailType,
            @RequestParam(value = "language", required = false) String language,
            @RequestParam(value = "status", required = false) String status,
            @RequestParam(value = "templateName", required = false) String templateName) {
        
        return emailTemplateService.queryTemplateList(pageNo, pageSize, emailType, language, status, templateName);
    }

    /**
     * 根据ID查询邮件模板
     *
     * @param id 模板ID
     * @return 模板信息
     */
    @GetMapping("/getById")
    public R<EmailTemplate> getTemplateById(@RequestParam("id") @NotNull(message = "模板ID不能为空") Long id) {
        return emailTemplateService.getTemplateById(id);
    }

    /**
     * 根据模板代码查询邮件模板
     *
     * @param templateCode 模板代码
     * @return 模板信息
     */
    @GetMapping("/getByCode")
    public R<EmailTemplate> getTemplateByCode(@RequestParam("templateCode") @NotBlank(message = "模板代码不能为空") String templateCode) {
        return emailTemplateService.getTemplateByCode(templateCode);
    }

    /**
     * 根据邮件类型获取默认模板
     *
     * @param emailType 邮件类型
     * @param language  语言
     * @return 模板信息
     */
    @GetMapping("/getDefault")
    public R<EmailTemplate> getDefaultTemplate(@RequestParam("emailType") @NotBlank(message = "邮件类型不能为空") String emailType,
                                              @RequestParam(value = "language", defaultValue = "zh_CN") String language) {
        return emailTemplateService.getDefaultTemplate(emailType, language);
    }

    /**
     * 根据邮件类型查询所有模板
     *
     * @param emailType 邮件类型
     * @return 模板列表
     */
    @GetMapping("/getByEmailType")
    public R<List<EmailTemplate>> getTemplatesByEmailType(@RequestParam("emailType") @NotBlank(message = "邮件类型不能为空") String emailType) {
        return emailTemplateService.getTemplatesByEmailType(emailType);
    }

    /**
     * 查询所有启用的模板
     *
     * @return 模板列表
     */
    @GetMapping("/getAllEnabled")
    public R<List<EmailTemplate>> getAllEnabledTemplates() {
        return emailTemplateService.getAllEnabledTemplates();
    }

    /**
     * 新增邮件模板
     *
     * @param template 模板信息
     * @return 操作结果
     */
    @PostMapping("/add")
    public R<String> addTemplate(@RequestBody @Validated EmailTemplate template) {
        return emailTemplateService.addTemplate(template);
    }

    /**
     * 更新邮件模板
     *
     * @param template 模板信息
     * @return 操作结果
     */
    @PostMapping("/update")
    public R<String> updateTemplate(@RequestBody @Validated EmailTemplate template) {
        return emailTemplateService.updateTemplate(template);
    }

    /**
     * 删除邮件模板
     *
     * @param id 模板ID
     * @return 操作结果
     */
    @PostMapping("/delete")
    public R<String> deleteTemplate(@RequestParam("id") @NotNull(message = "模板ID不能为空") Long id) {
        return emailTemplateService.deleteTemplate(id);
    }

    /**
     * 设置默认模板
     *
     * @param id 模板ID
     * @return 操作结果
     */
    @PostMapping("/setDefault")
    public R<String> setDefaultTemplate(@RequestParam("id") @NotNull(message = "模板ID不能为空") Long id) {
        return emailTemplateService.setDefaultTemplate(id);
    }

    /**
     * 渲染邮件模板
     *
     * @param templateCode 模板代码
     * @param variables    变量Map
     * @return 渲染结果
     */
    @PostMapping("/render")
    public R<Map<String, String>> renderTemplate(@RequestParam("templateCode") @NotBlank(message = "模板代码不能为空") String templateCode,
                                                 @RequestBody(required = false) Map<String, Object> variables) {
        return emailTemplateService.renderTemplate(templateCode, variables);
    }

    /**
     * 根据邮件类型渲染模板
     *
     * @param emailType 邮件类型
     * @param language  语言
     * @param variables 变量Map
     * @return 渲染结果
     */
    @PostMapping("/renderByType")
    public R<Map<String, String>> renderTemplateByType(@RequestParam("emailType") @NotBlank(message = "邮件类型不能为空") String emailType,
                                                       @RequestParam(value = "language", defaultValue = "zh_CN") String language,
                                                       @RequestBody(required = false) Map<String, Object> variables) {
        return emailTemplateService.renderTemplateByType(emailType, language, variables);
    }

    /**
     * 验证模板语法
     *
     * @param request 验证请求
     * @return 验证结果
     */
    @PostMapping("/validate")
    public R<String> validateTemplate(@RequestBody ValidateTemplateRequest request) {
        return emailTemplateService.validateTemplate(request.getSubjectTemplate(), request.getContentTemplate(), request.getVariables());
    }

    /**
     * 复制模板
     *
     * @param request 复制请求
     * @return 操作结果
     */
    @PostMapping("/copy")
    public R<String> copyTemplate(@RequestBody CopyTemplateRequest request) {
        return emailTemplateService.copyTemplate(request.getSourceId(), request.getNewCode(), request.getNewName(), request.getCreateUser());
    }

    /**
     * 导入模板
     *
     * @param templates 模板列表
     * @return 操作结果
     */
    @PostMapping("/import")
    public R<String> importTemplates(@RequestBody List<EmailTemplate> templates) {
        return emailTemplateService.importTemplates(templates);
    }

    /**
     * 导出模板
     *
     * @param emailType 邮件类型
     * @param language  语言
     * @return 模板列表
     */
    @GetMapping("/export")
    public R<List<EmailTemplate>> exportTemplates(@RequestParam(value = "emailType", required = false) String emailType,
                                                  @RequestParam(value = "language", required = false) String language) {
        return emailTemplateService.exportTemplates(emailType, language);
    }

    /**
     * 验证模板请求
     */
    public static class ValidateTemplateRequest {
        private String subjectTemplate;
        private String contentTemplate;
        private Map<String, Object> variables;

        public String getSubjectTemplate() {
            return subjectTemplate;
        }

        public void setSubjectTemplate(String subjectTemplate) {
            this.subjectTemplate = subjectTemplate;
        }

        public String getContentTemplate() {
            return contentTemplate;
        }

        public void setContentTemplate(String contentTemplate) {
            this.contentTemplate = contentTemplate;
        }

        public Map<String, Object> getVariables() {
            return variables;
        }

        public void setVariables(Map<String, Object> variables) {
            this.variables = variables;
        }
    }

    /**
     * 复制模板请求
     */
    public static class CopyTemplateRequest {
        private Long sourceId;
        private String newCode;
        private String newName;
        private String createUser;

        public Long getSourceId() {
            return sourceId;
        }

        public void setSourceId(Long sourceId) {
            this.sourceId = sourceId;
        }

        public String getNewCode() {
            return newCode;
        }

        public void setNewCode(String newCode) {
            this.newCode = newCode;
        }

        public String getNewName() {
            return newName;
        }

        public void setNewName(String newName) {
            this.newName = newName;
        }

        public String getCreateUser() {
            return createUser;
        }

        public void setCreateUser(String createUser) {
            this.createUser = createUser;
        }
    }
}
