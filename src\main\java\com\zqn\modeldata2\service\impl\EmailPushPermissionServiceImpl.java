package com.zqn.modeldata2.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.zqn.modeldata2.common.R;
import com.zqn.modeldata2.entity.email.EmailPermissionQuery;
import com.zqn.modeldata2.entity.email.EmailPushPermission;
import com.zqn.modeldata2.mapper.EmailPushPermissionMapper;
import com.zqn.modeldata2.service.EmailPushPermissionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 邮件推送权限配置Service实现类
 * @date 2025/01/22 10:00
 */
@Slf4j
@Service
public class EmailPushPermissionServiceImpl implements EmailPushPermissionService {

    @Resource
    private EmailPushPermissionMapper emailPushPermissionMapper;

    @Override
    public R<PageInfo<EmailPushPermission>> queryPermissionList(EmailPermissionQuery query) {
        try {
            PageHelper.startPage(query.getPageNo(), query.getPageSize());
            List<EmailPushPermission> list = emailPushPermissionMapper.selectByCondition(query);
            PageInfo<EmailPushPermission> pageInfo = new PageInfo<>(list);
            return R.success(pageInfo);
        } catch (Exception e) {
            log.error("查询邮件推送权限列表失败", e);
            return R.error("查询邮件推送权限列表失败：" + e.getMessage());
        }
    }

    @Override
    public R<EmailPushPermission> getPermissionByUserNo(String userNo) {
        try {
            if (!StringUtils.hasText(userNo)) {
                return R.error("工号不能为空");
            }
            EmailPushPermission permission = emailPushPermissionMapper.selectByUserNo(userNo);
            return R.success(permission);
        } catch (Exception e) {
            log.error("根据工号查询邮件推送权限失败，userNo: {}", userNo, e);
            return R.error("查询邮件推送权限失败：" + e.getMessage());
        }
    }

    @Override
    public R<List<EmailPushPermission>> getPermissionsByEmailType(String emailType) {
        try {
            if (!StringUtils.hasText(emailType)) {
                return R.error("邮件类型不能为空");
            }
            List<EmailPushPermission> permissions = emailPushPermissionMapper.selectByEmailType(emailType);
            return R.success(permissions);
        } catch (Exception e) {
            log.error("根据邮件类型查询权限失败，emailType: {}", emailType, e);
            return R.error("查询权限失败：" + e.getMessage());
        }
    }

    @Override
    public R<List<EmailPushPermission>> getPermissionsByEmailTypeAndDept(String emailType, String deptName) {
        try {
            if (!StringUtils.hasText(emailType)) {
                return R.error("邮件类型不能为空");
            }
            List<EmailPushPermission> permissions = emailPushPermissionMapper.selectByEmailTypeAndDept(emailType, deptName);
            return R.success(permissions);
        } catch (Exception e) {
            log.error("根据邮件类型和部门查询权限失败，emailType: {}, deptName: {}", emailType, deptName, e);
            return R.error("查询权限失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<String> addPermission(EmailPushPermission permission) {
        try {
            if (!StringUtils.hasText(permission.getUserNo())) {
                return R.error("工号不能为空");
            }
            
            // 检查工号是否已存在
            EmailPushPermission existing = emailPushPermissionMapper.selectByUserNo(permission.getUserNo());
            if (existing != null) {
                return R.error("该工号的权限配置已存在");
            }
            
            // 设置默认值
            setDefaultValues(permission);
            permission.setCreateDate(new Date());
            
            int result = emailPushPermissionMapper.insert(permission);
            if (result > 0) {
                return R.success("新增权限配置成功");
            } else {
                return R.error("新增权限配置失败");
            }
        } catch (Exception e) {
            log.error("新增邮件推送权限失败", e);
            return R.error("新增权限配置失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<String> updatePermission(EmailPushPermission permission) {
        try {
            if (!StringUtils.hasText(permission.getUserNo())) {
                return R.error("工号不能为空");
            }
            
            permission.setUpdateDate(new Date());
            int result = emailPushPermissionMapper.updateByUserNo(permission);
            if (result > 0) {
                return R.success("更新权限配置成功");
            } else {
                return R.error("更新权限配置失败，权限配置不存在");
            }
        } catch (Exception e) {
            log.error("更新邮件推送权限失败", e);
            return R.error("更新权限配置失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<String> deletePermission(String userNo) {
        try {
            if (!StringUtils.hasText(userNo)) {
                return R.error("工号不能为空");
            }
            
            int result = emailPushPermissionMapper.deleteByUserNo(userNo);
            if (result > 0) {
                return R.success("删除权限配置成功");
            } else {
                return R.error("删除权限配置失败，权限配置不存在");
            }
        } catch (Exception e) {
            log.error("删除邮件推送权限失败，userNo: {}", userNo, e);
            return R.error("删除权限配置失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<String> batchAddPermissions(List<EmailPushPermission> permissions) {
        try {
            if (permissions == null || permissions.isEmpty()) {
                return R.error("权限配置列表不能为空");
            }
            
            // 设置默认值
            Date now = new Date();
            for (EmailPushPermission permission : permissions) {
                setDefaultValues(permission);
                permission.setCreateDate(now);
            }
            
            int result = emailPushPermissionMapper.batchInsert(permissions);
            return R.success("批量新增权限配置成功，共新增 " + result + " 条记录");
        } catch (Exception e) {
            log.error("批量新增邮件推送权限失败", e);
            return R.error("批量新增权限配置失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<String> batchUpdatePermissions(List<EmailPushPermission> permissions) {
        try {
            if (permissions == null || permissions.isEmpty()) {
                return R.error("权限配置列表不能为空");
            }
            
            // 设置更新时间
            Date now = new Date();
            for (EmailPushPermission permission : permissions) {
                permission.setUpdateDate(now);
            }
            
            int result = emailPushPermissionMapper.batchUpdate(permissions);
            return R.success("批量更新权限配置成功，共更新 " + result + " 条记录");
        } catch (Exception e) {
            log.error("批量更新邮件推送权限失败", e);
            return R.error("批量更新权限配置失败：" + e.getMessage());
        }
    }

    @Override
    public R<List<EmailPushPermission>> getPermissionsByUserNos(List<String> userNos) {
        try {
            if (userNos == null || userNos.isEmpty()) {
                return R.error("工号列表不能为空");
            }
            List<EmailPushPermission> permissions = emailPushPermissionMapper.selectByUserNos(userNos);
            return R.success(permissions);
        } catch (Exception e) {
            log.error("根据工号列表查询权限失败", e);
            return R.error("查询权限失败：" + e.getMessage());
        }
    }

    @Override
    public R<List<String>> getAllDepts() {
        try {
            List<String> depts = emailPushPermissionMapper.selectAllDepts();
            return R.success(depts);
        } catch (Exception e) {
            log.error("查询所有部门失败", e);
            return R.error("查询部门列表失败：" + e.getMessage());
        }
    }

    @Override
    public List<EmailPushPermission> getValidRecipients(String emailType) {
        try {
            List<EmailPushPermission> permissions = emailPushPermissionMapper.selectByEmailType(emailType);
            // 过滤出有邮箱地址且状态为启用的用户
            return permissions.stream()
                    .filter(p -> StringUtils.hasText(p.getEmail()) && "Y".equals(p.getStatus()))
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取有效收件人列表失败，emailType: {}", emailType, e);
            return null;
        }
    }

    @Override
    public boolean hasPermission(String userNo, String emailType) {
        try {
            EmailPushPermission permission = emailPushPermissionMapper.selectByUserNo(userNo);
            if (permission == null || !"Y".equals(permission.getStatus())) {
                return false;
            }
            
            // 根据邮件类型检查对应的权限字段
            return checkPermissionByEmailType(permission, emailType);
        } catch (Exception e) {
            log.error("检查用户权限失败，userNo: {}, emailType: {}", userNo, emailType, e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<String> syncUserPermissions() {
        try {
            // TODO: 实现从sy_user表同步用户信息的逻辑
            // 这里需要根据实际的sy_user表结构来实现
            log.info("同步用户权限功能待实现");
            return R.success("同步用户权限成功");
        } catch (Exception e) {
            log.error("同步用户权限失败", e);
            return R.error("同步用户权限失败：" + e.getMessage());
        }
    }

    /**
     * 设置默认值
     */
    private void setDefaultValues(EmailPushPermission permission) {
        if (permission.getPushQuoteV1() == null) permission.setPushQuoteV1("N");
        if (permission.getPushQuoteV2() == null) permission.setPushQuoteV2("N");
        if (permission.getPushQuoteV3() == null) permission.setPushQuoteV3("N");
        if (permission.getPushQuoteV4() == null) permission.setPushQuoteV4("N");
        if (permission.getPushQuoteV5() == null) permission.setPushQuoteV5("N");
        if (permission.getPushEstimateZ() == null) permission.setPushEstimateZ("N");
        if (permission.getPushEstimateZz() == null) permission.setPushEstimateZz("N");
        if (permission.getPushPVersion() == null) permission.setPushPVersion("N");
        if (permission.getUpdateEmailNotify() == null) permission.setUpdateEmailNotify("Y");
        if (permission.getStatus() == null) permission.setStatus("Y");
    }

    /**
     * 根据邮件类型检查权限
     */
    private boolean checkPermissionByEmailType(EmailPushPermission permission, String emailType) {
        switch (emailType) {
            case "QUOTE_V1":
                return "Y".equals(permission.getPushQuoteV1());
            case "QUOTE_V2":
                return "Y".equals(permission.getPushQuoteV2());
            case "QUOTE_V3":
                return "Y".equals(permission.getPushQuoteV3());
            case "QUOTE_V4":
                return "Y".equals(permission.getPushQuoteV4());
            case "QUOTE_V5":
                return "Y".equals(permission.getPushQuoteV5());
            case "ESTIMATE_Z":
                return "Y".equals(permission.getPushEstimateZ());
            case "ESTIMATE_ZZ":
                return "Y".equals(permission.getPushEstimateZz());
            case "P_VERSION":
                return "Y".equals(permission.getPushPVersion());
            case "UPDATE_NOTIFY":
                return "Y".equals(permission.getUpdateEmailNotify());
            default:
                return false;
        }
    }
}
