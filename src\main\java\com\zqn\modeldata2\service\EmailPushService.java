package com.zqn.modeldata2.service;

import com.github.pagehelper.PageInfo;
import com.zqn.modeldata2.common.R;
import com.zqn.modeldata2.entity.email.EmailPushLog;
import com.zqn.modeldata2.entity.email.EmailPushRequest;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 邮件推送Service接口
 * @date 2025/01/22 10:00
 */
public interface EmailPushService {

    /**
     * 发送邮件
     *
     * @param request 邮件推送请求
     * @return 操作结果
     */
    R<String> sendEmail(EmailPushRequest request);

    /**
     * 批量发送邮件
     *
     * @param requests 邮件推送请求列表
     * @return 操作结果
     */
    R<String> batchSendEmail(List<EmailPushRequest> requests);

    /**
     * 发送报价邮件
     *
     * @param quoteVersion 报价版本 (V1, V2, V3, V4, V5)
     * @param businessId   业务ID
     * @param sendUser     发送人
     * @return 操作结果
     */
    R<String> sendQuoteEmail(String quoteVersion, String businessId, String sendUser);

    /**
     * 发送预估邮件
     *
     * @param estimateVersion 预估版本 (Z, ZZ)
     * @param businessId      业务ID
     * @param sendUser        发送人
     * @return 操作结果
     */
    R<String> sendEstimateEmail(String estimateVersion, String businessId, String sendUser);

    /**
     * 发送P版邮件
     *
     * @param businessId 业务ID
     * @param sendUser   发送人
     * @return 操作结果
     */
    R<String> sendPVersionEmail(String businessId, String sendUser);

    /**
     * 发送版本更新通知邮件
     *
     * @param businessId 业务ID
     * @param sendUser   发送人
     * @return 操作结果
     */
    R<String> sendUpdateNotifyEmail(String businessId, String sendUser);

    /**
     * 重试失败的邮件
     *
     * @param logId 日志ID
     * @return 操作结果
     */
    R<String> retryFailedEmail(Long logId);

    /**
     * 批量重试失败的邮件
     *
     * @return 操作结果
     */
    R<String> batchRetryFailedEmails();

    /**
     * 分页查询邮件发送日志
     *
     * @param pageNo     页码
     * @param pageSize   页大小
     * @param emailType  邮件类型
     * @param sendStatus 发送状态
     * @param userNo     用户工号
     * @param businessId 业务ID
     * @param startDate  开始日期
     * @param endDate    结束日期
     * @return 分页结果
     */
    R<PageInfo<EmailPushLog>> queryEmailLogs(int pageNo, int pageSize, String emailType, 
                                             String sendStatus, String userNo, String businessId, 
                                             Date startDate, Date endDate);

    /**
     * 根据批次ID查询邮件日志
     *
     * @param batchId 批次ID
     * @return 日志列表
     */
    R<List<EmailPushLog>> getEmailLogsByBatchId(String batchId);

    /**
     * 根据业务ID查询邮件日志
     *
     * @param businessId 业务ID
     * @return 日志列表
     */
    R<List<EmailPushLog>> getEmailLogsByBusinessId(String businessId);

    /**
     * 获取邮件发送统计信息
     *
     * @param emailType 邮件类型
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 统计结果
     */
    R<List<EmailPushLog>> getEmailSendStatistics(String emailType, Date startDate, Date endDate);

    /**
     * 清理历史邮件日志
     *
     * @param beforeDate 指定日期之前的日志将被清理
     * @return 操作结果
     */
    R<String> cleanHistoryLogs(Date beforeDate);

    /**
     * 测试邮件发送配置
     *
     * @param testEmail 测试邮箱
     * @return 操作结果
     */
    R<String> testEmailConfig(String testEmail);
}
