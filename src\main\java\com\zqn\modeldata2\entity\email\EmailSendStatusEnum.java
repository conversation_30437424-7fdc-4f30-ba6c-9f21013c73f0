package com.zqn.modeldata2.entity.email;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 邮件发送状态枚举
 * @date 2025/01/22 10:00
 */
public enum EmailSendStatusEnum {

    /**
     * 待发送
     */
    PENDING("PENDING", "待发送"),

    /**
     * 发送中
     */
    SENDING("SENDING", "发送中"),

    /**
     * 发送成功
     */
    SUCCESS("SUCCESS", "发送成功"),

    /**
     * 发送失败
     */
    FAILED("FAILED", "发送失败"),

    /**
     * 重试中
     */
    RETRY("RETRY", "重试中");

    private final String code;
    private final String description;

    EmailSendStatusEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据代码获取枚举
     *
     * @param code 代码
     * @return 枚举
     */
    public static EmailSendStatusEnum getByCode(String code) {
        for (EmailSendStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 检查代码是否有效
     *
     * @param code 代码
     * @return 是否有效
     */
    public static boolean isValidCode(String code) {
        return getByCode(code) != null;
    }

    /**
     * 获取所有状态代码
     *
     * @return 代码数组
     */
    public static String[] getAllCodes() {
        EmailSendStatusEnum[] values = values();
        String[] codes = new String[values.length];
        for (int i = 0; i < values.length; i++) {
            codes[i] = values[i].getCode();
        }
        return codes;
    }

    /**
     * 检查是否为最终状态（成功或失败）
     *
     * @return 是否为最终状态
     */
    public boolean isFinalStatus() {
        return this == SUCCESS || this == FAILED;
    }

    /**
     * 检查是否为处理中状态
     *
     * @return 是否为处理中状态
     */
    public boolean isProcessingStatus() {
        return this == PENDING || this == SENDING || this == RETRY;
    }

    /**
     * 检查是否可以重试
     *
     * @return 是否可以重试
     */
    public boolean canRetry() {
        return this == FAILED;
    }

    /**
     * 检查是否为成功状态
     *
     * @return 是否为成功状态
     */
    public boolean isSuccess() {
        return this == SUCCESS;
    }

    /**
     * 检查是否为失败状态
     *
     * @return 是否为失败状态
     */
    public boolean isFailed() {
        return this == FAILED;
    }
}
