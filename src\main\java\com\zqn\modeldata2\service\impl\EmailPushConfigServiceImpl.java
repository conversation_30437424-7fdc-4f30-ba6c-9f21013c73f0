package com.zqn.modeldata2.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.zqn.modeldata2.common.R;
import com.zqn.modeldata2.entity.email.EmailPushConfig;
import com.zqn.modeldata2.mapper.EmailPushConfigMapper;
import com.zqn.modeldata2.service.EmailPushConfigService;
import com.zqn.modeldata2.service.EmailSendService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 邮件推送配置Service实现类
 * @date 2025/01/22 10:00
 */
@Slf4j
@Service
public class EmailPushConfigServiceImpl implements EmailPushConfigService {

    @Resource
    private EmailPushConfigMapper emailPushConfigMapper;

    @Resource
    private EmailSendService emailSendService;

    /**
     * 配置缓存
     */
    private final Map<String, String> configCache = new ConcurrentHashMap<>();

    @Override
    public R<PageInfo<EmailPushConfig>> queryConfigList(int pageNo, int pageSize, String configKey, 
                                                       String configType, String status) {
        try {
            PageHelper.startPage(pageNo, pageSize);
            List<EmailPushConfig> list = emailPushConfigMapper.selectByCondition(configKey, configType, status);
            PageInfo<EmailPushConfig> pageInfo = new PageInfo<>(list);
            return R.success(pageInfo);
        } catch (Exception e) {
            log.error("查询邮件推送配置列表失败", e);
            return R.error("查询邮件推送配置列表失败：" + e.getMessage());
        }
    }

    @Override
    public R<EmailPushConfig> getConfigById(Long id) {
        try {
            if (id == null) {
                return R.error("配置ID不能为空");
            }
            EmailPushConfig config = emailPushConfigMapper.selectById(id);
            return R.success(config);
        } catch (Exception e) {
            log.error("根据ID查询配置失败，id: {}", id, e);
            return R.error("查询配置失败：" + e.getMessage());
        }
    }

    @Override
    public R<EmailPushConfig> getConfigByKey(String configKey) {
        try {
            if (!StringUtils.hasText(configKey)) {
                return R.error("配置键不能为空");
            }
            EmailPushConfig config = emailPushConfigMapper.selectByConfigKey(configKey);
            return R.success(config);
        } catch (Exception e) {
            log.error("根据配置键查询配置失败，configKey: {}", configKey, e);
            return R.error("查询配置失败：" + e.getMessage());
        }
    }

    @Override
    public String getConfigValue(String configKey) {
        return getConfigValue(configKey, null);
    }

    @Override
    public String getConfigValue(String configKey, String defaultValue) {
        try {
            if (!StringUtils.hasText(configKey)) {
                return defaultValue;
            }

            // 先从缓存获取
            String cachedValue = configCache.get(configKey);
            if (cachedValue != null) {
                return cachedValue;
            }

            // 从数据库获取
            EmailPushConfig config = emailPushConfigMapper.selectByConfigKey(configKey);
            if (config != null && "Y".equals(config.getStatus())) {
                String value = config.getConfigValue();
                // 放入缓存
                configCache.put(configKey, value);
                return value;
            }

            return defaultValue;
        } catch (Exception e) {
            log.error("获取配置值失败，configKey: {}", configKey, e);
            return defaultValue;
        }
    }

    @Override
    public R<List<EmailPushConfig>> getConfigsByType(String configType) {
        try {
            if (!StringUtils.hasText(configType)) {
                return R.error("配置类型不能为空");
            }
            List<EmailPushConfig> configs = emailPushConfigMapper.selectByConfigType(configType);
            return R.success(configs);
        } catch (Exception e) {
            log.error("根据配置类型查询配置失败，configType: {}", configType, e);
            return R.error("查询配置失败：" + e.getMessage());
        }
    }

    @Override
    public R<List<EmailPushConfig>> getAllEnabledConfigs() {
        try {
            List<EmailPushConfig> configs = emailPushConfigMapper.selectAllEnabled();
            return R.success(configs);
        } catch (Exception e) {
            log.error("查询所有启用的配置失败", e);
            return R.error("查询配置失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<String> addConfig(EmailPushConfig config) {
        try {
            // 参数验证
            if (!StringUtils.hasText(config.getConfigKey())) {
                return R.error("配置键不能为空");
            }
            if (!StringUtils.hasText(config.getConfigValue())) {
                return R.error("配置值不能为空");
            }

            // 检查配置键是否已存在
            int count = emailPushConfigMapper.checkConfigKeyExists(config.getConfigKey(), null);
            if (count > 0) {
                return R.error("配置键已存在");
            }

            // 设置默认值
            setDefaultValues(config);
            config.setCreateDate(new Date());

            int result = emailPushConfigMapper.insert(config);
            if (result > 0) {
                // 清除缓存
                configCache.remove(config.getConfigKey());
                return R.success("新增配置成功");
            } else {
                return R.error("新增配置失败");
            }
        } catch (Exception e) {
            log.error("新增配置失败", e);
            return R.error("新增配置失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<String> updateConfig(EmailPushConfig config) {
        try {
            if (config.getId() == null) {
                return R.error("配置ID不能为空");
            }

            // 检查配置键是否已存在（排除当前记录）
            if (StringUtils.hasText(config.getConfigKey())) {
                int count = emailPushConfigMapper.checkConfigKeyExists(config.getConfigKey(), config.getId());
                if (count > 0) {
                    return R.error("配置键已存在");
                }
            }

            config.setUpdateDate(new Date());
            int result = emailPushConfigMapper.updateById(config);
            if (result > 0) {
                // 清除缓存
                configCache.remove(config.getConfigKey());
                return R.success("更新配置成功");
            } else {
                return R.error("更新配置失败，配置不存在");
            }
        } catch (Exception e) {
            log.error("更新配置失败", e);
            return R.error("更新配置失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<String> updateConfigValue(String configKey, String configValue, String updateUser) {
        try {
            if (!StringUtils.hasText(configKey)) {
                return R.error("配置键不能为空");
            }
            if (!StringUtils.hasText(configValue)) {
                return R.error("配置值不能为空");
            }

            int result = emailPushConfigMapper.updateValueByKey(configKey, configValue, updateUser);
            if (result > 0) {
                // 清除缓存
                configCache.remove(configKey);
                return R.success("更新配置值成功");
            } else {
                return R.error("更新配置值失败，配置不存在");
            }
        } catch (Exception e) {
            log.error("更新配置值失败，configKey: {}", configKey, e);
            return R.error("更新配置值失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<String> deleteConfig(Long id) {
        try {
            if (id == null) {
                return R.error("配置ID不能为空");
            }

            // 获取配置信息用于清除缓存
            EmailPushConfig config = emailPushConfigMapper.selectById(id);
            
            int result = emailPushConfigMapper.deleteById(id);
            if (result > 0) {
                // 清除缓存
                if (config != null) {
                    configCache.remove(config.getConfigKey());
                }
                return R.success("删除配置成功");
            } else {
                return R.error("删除配置失败，配置不存在");
            }
        } catch (Exception e) {
            log.error("删除配置失败，id: {}", id, e);
            return R.error("删除配置失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<String> batchUpdateConfigs(List<EmailPushConfig> configs) {
        try {
            if (configs == null || configs.isEmpty()) {
                return R.error("配置列表不能为空");
            }

            // 设置更新时间
            Date now = new Date();
            for (EmailPushConfig config : configs) {
                config.setUpdateDate(now);
            }

            int result = emailPushConfigMapper.batchUpdate(configs);
            
            // 清除相关缓存
            for (EmailPushConfig config : configs) {
                configCache.remove(config.getConfigKey());
            }

            return R.success("批量更新配置成功，共更新 " + result + " 条记录");
        } catch (Exception e) {
            log.error("批量更新配置失败", e);
            return R.error("批量更新配置失败：" + e.getMessage());
        }
    }

    @Override
    public Map<String, String> getSmtpConfig() {
        try {
            Map<String, String> smtpConfig = new HashMap<>();
            smtpConfig.put("host", getConfigValue("SMTP_HOST", ""));
            smtpConfig.put("port", getConfigValue("SMTP_PORT", "587"));
            smtpConfig.put("username", getConfigValue("SMTP_USERNAME", ""));
            smtpConfig.put("password", getConfigValue("SMTP_PASSWORD", ""));
            smtpConfig.put("auth", getConfigValue("SMTP_AUTH", "true"));
            smtpConfig.put("starttls", getConfigValue("SMTP_STARTTLS", "true"));
            smtpConfig.put("ssl", getConfigValue("SMTP_SSL", "false"));
            return smtpConfig;
        } catch (Exception e) {
            log.error("获取SMTP配置失败", e);
            return new HashMap<>();
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<String> updateSmtpConfig(Map<String, String> smtpConfig, String updateUser) {
        try {
            if (smtpConfig == null || smtpConfig.isEmpty()) {
                return R.error("SMTP配置不能为空");
            }

            int successCount = 0;
            for (Map.Entry<String, String> entry : smtpConfig.entrySet()) {
                String configKey = "SMTP_" + entry.getKey().toUpperCase();
                String configValue = entry.getValue();
                
                try {
                    R<String> result = updateConfigValue(configKey, configValue, updateUser);
                    if (result.getCode() == 1) {
                        successCount++;
                    }
                } catch (Exception e) {
                    log.error("更新SMTP配置失败，key: {}", configKey, e);
                }
            }

            return R.success("更新SMTP配置完成，共处理 " + smtpConfig.size() + " 个配置，成功 " + successCount + " 个");
        } catch (Exception e) {
            log.error("更新SMTP配置失败", e);
            return R.error("更新SMTP配置失败：" + e.getMessage());
        }
    }

    @Override
    public R<String> testSmtpConnection() {
        try {
            Map<String, String> smtpConfig = getSmtpConfig();
            
            // 检查必要的配置
            if (!StringUtils.hasText(smtpConfig.get("host"))) {
                return R.error("SMTP主机未配置");
            }
            if (!StringUtils.hasText(smtpConfig.get("username"))) {
                return R.error("SMTP用户名未配置");
            }
            if (!StringUtils.hasText(smtpConfig.get("password"))) {
                return R.error("SMTP密码未配置");
            }

            // 使用邮件发送服务测试SMTP连接
            log.info("测试SMTP连接，主机: {}", smtpConfig.get("host"));
            boolean success = emailSendService.testSmtpConnection();

            if (success) {
                return R.success("SMTP连接测试成功");
            } else {
                return R.error("SMTP连接测试失败，请检查配置");
            }
        } catch (Exception e) {
            log.error("测试SMTP连接失败", e);
            return R.error("测试SMTP连接失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<String> resetConfigToDefault(String configKey) {
        try {
            if (!StringUtils.hasText(configKey)) {
                return R.error("配置键不能为空");
            }

            // 获取默认值
            String defaultValue = getDefaultConfigValue(configKey);
            if (defaultValue == null) {
                return R.error("未找到配置的默认值");
            }

            R<String> result = updateConfigValue(configKey, defaultValue, "SYSTEM");
            if (result.getCode() == 1) {
                return R.success("重置配置为默认值成功");
            } else {
                return result;
            }
        } catch (Exception e) {
            log.error("重置配置为默认值失败，configKey: {}", configKey, e);
            return R.error("重置配置为默认值失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<String> importConfigs(List<EmailPushConfig> configs) {
        try {
            if (configs == null || configs.isEmpty()) {
                return R.error("配置列表不能为空");
            }

            int successCount = 0;
            for (EmailPushConfig config : configs) {
                try {
                    R<String> result = addConfig(config);
                    if (result.getCode() == 1) {
                        successCount++;
                    }
                } catch (Exception e) {
                    log.error("导入配置失败，configKey: {}", config.getConfigKey(), e);
                }
            }

            return R.success("导入配置完成，共处理 " + configs.size() + " 个配置，成功 " + successCount + " 个");
        } catch (Exception e) {
            log.error("导入配置失败", e);
            return R.error("导入配置失败：" + e.getMessage());
        }
    }

    @Override
    public R<List<EmailPushConfig>> exportConfigs(String configType) {
        try {
            List<EmailPushConfig> configs;
            if (StringUtils.hasText(configType)) {
                configs = emailPushConfigMapper.selectByConfigType(configType);
            } else {
                configs = emailPushConfigMapper.selectAllEnabled();
            }
            return R.success(configs);
        } catch (Exception e) {
            log.error("导出配置失败", e);
            return R.error("导出配置失败：" + e.getMessage());
        }
    }

    @Override
    public R<String> refreshConfigCache() {
        try {
            configCache.clear();
            log.info("配置缓存已清空");
            return R.success("刷新配置缓存成功");
        } catch (Exception e) {
            log.error("刷新配置缓存失败", e);
            return R.error("刷新配置缓存失败：" + e.getMessage());
        }
    }

    /**
     * 设置默认值
     */
    private void setDefaultValues(EmailPushConfig config) {
        if (config.getConfigType() == null) config.setConfigType("STRING");
        if (config.getStatus() == null) config.setStatus("Y");
    }

    /**
     * 获取配置的默认值
     */
    private String getDefaultConfigValue(String configKey) {
        Map<String, String> defaultValues = new HashMap<>();
        defaultValues.put("SMTP_HOST", "smtp.gmail.com");
        defaultValues.put("SMTP_PORT", "587");
        defaultValues.put("SMTP_AUTH", "true");
        defaultValues.put("SMTP_STARTTLS", "true");
        defaultValues.put("SMTP_SSL", "false");
        defaultValues.put("MAX_RETRY_COUNT", "3");
        defaultValues.put("RETRY_INTERVAL", "30");
        defaultValues.put("BATCH_SIZE", "50");
        defaultValues.put("EMAIL_FROM", "<EMAIL>");
        
        return defaultValues.get(configKey);
    }
}
