-- 邮件推送权限配置表
CREATE TABLE email_push_permission (
    id NUMBER(10) PRIMARY KEY,                    -- 主键ID
    user_no VARCHAR2(20) NOT NULL,               -- 工号（关联sy_user.user_no）
    dept_name VARCHAR2(100),                     -- 部门名称
    email VARCHAR2(200),                         -- 邮箱地址
    
    -- 推送权限字段 (Y-有权限, N-无权限, P-待定)
    push_quote_v1 CHAR(1) DEFAULT 'N',           -- 推送報價1版權限
    push_quote_v2 CHAR(1) DEFAULT 'N',           -- 推送報價2版權限  
    push_quote_v3 CHAR(1) DEFAULT 'N',           -- 推送報價3版權限
    push_quote_v4 CHAR(1) DEFAULT 'N',           -- 推送報價4版權限
    push_quote_v5 CHAR(1) DEFAULT 'N',           -- 推送報價5版權限
    push_estimate_z CHAR(1) DEFAULT 'N',         -- 推送預估Z版權限
    push_estimate_zz CHAR(1) DEFAULT 'N',        -- 推送預估ZZ版權限
    push_p_version CHAR(1) DEFAULT 'N',          -- 推送P版權限
    update_email_notify CHAR(1) DEFAULT 'N',     -- 更新版本郵件提醒
    
    -- 审核状态和备注
    status CHAR(1) DEFAULT 'Y',                  -- 状态 (Y-启用, N-禁用)
    remark VARCHAR2(500),                        -- 备注说明
    
    -- 审计字段
    create_user VARCHAR2(20),                    -- 创建人
    create_date DATE DEFAULT SYSDATE,           -- 创建时间
    update_user VARCHAR2(20),                   -- 更新人
    update_date DATE DEFAULT SYSDATE,           -- 更新时间
    
    -- 约束
    CONSTRAINT chk_push_quote_v1 CHECK (push_quote_v1 IN ('Y', 'N', 'P')),
    CONSTRAINT chk_push_quote_v2 CHECK (push_quote_v2 IN ('Y', 'N', 'P')),
    CONSTRAINT chk_push_quote_v3 CHECK (push_quote_v3 IN ('Y', 'N', 'P')),
    CONSTRAINT chk_push_quote_v4 CHECK (push_quote_v4 IN ('Y', 'N', 'P')),
    CONSTRAINT chk_push_quote_v5 CHECK (push_quote_v5 IN ('Y', 'N', 'P')),
    CONSTRAINT chk_push_estimate_z CHECK (push_estimate_z IN ('Y', 'N', 'P')),
    CONSTRAINT chk_push_estimate_zz CHECK (push_estimate_zz IN ('Y', 'N', 'P')),
    CONSTRAINT chk_push_p_version CHECK (push_p_version IN ('Y', 'N', 'P')),
    CONSTRAINT chk_update_email_notify CHECK (update_email_notify IN ('Y', 'N', 'P')),
    CONSTRAINT chk_status CHECK (status IN ('Y', 'N'))
);

-- 创建索引
CREATE INDEX idx_email_push_user_no ON email_push_permission(user_no);
CREATE INDEX idx_email_push_dept ON email_push_permission(dept_name);
CREATE INDEX idx_email_push_email ON email_push_permission(email);

-- 创建序列
CREATE SEQUENCE seq_email_push_permission START WITH 1 INCREMENT BY 1;

-- 添加注释
COMMENT ON TABLE email_push_permission IS '邮件推送权限配置表';
COMMENT ON COLUMN email_push_permission.id IS '主键ID';
COMMENT ON COLUMN email_push_permission.user_no IS '工号（关联sy_user.user_no）';
COMMENT ON COLUMN email_push_permission.dept_name IS '部门名称';
COMMENT ON COLUMN email_push_permission.email IS '邮箱地址';
COMMENT ON COLUMN email_push_permission.push_quote_v1 IS '推送報價1版權限 (Y-有权限, N-无权限, P-待定)';
COMMENT ON COLUMN email_push_permission.push_quote_v2 IS '推送報價2版權限 (Y-有权限, N-无权限, P-待定)';
COMMENT ON COLUMN email_push_permission.push_quote_v3 IS '推送報價3版權限 (Y-有权限, N-无权限, P-待定)';
COMMENT ON COLUMN email_push_permission.push_quote_v4 IS '推送報價4版權限 (Y-有权限, N-无权限, P-待定)';
COMMENT ON COLUMN email_push_permission.push_quote_v5 IS '推送報價5版權限 (Y-有权限, N-无权限, P-待定)';
COMMENT ON COLUMN email_push_permission.push_estimate_z IS '推送預估Z版權限 (Y-有权限, N-无权限, P-待定)';
COMMENT ON COLUMN email_push_permission.push_estimate_zz IS '推送預估ZZ版權限 (Y-有权限, N-无权限, P-待定)';
COMMENT ON COLUMN email_push_permission.push_p_version IS '推送P版權限 (Y-有权限, N-无权限, P-待定)';
COMMENT ON COLUMN email_push_permission.update_email_notify IS '更新版本郵件提醒 (Y-有权限, N-无权限, P-待定)';
COMMENT ON COLUMN email_push_permission.status IS '状态 (Y-启用, N-禁用)';
COMMENT ON COLUMN email_push_permission.remark IS '备注说明';
COMMENT ON COLUMN email_push_permission.create_user IS '创建人';
COMMENT ON COLUMN email_push_permission.create_date IS '创建时间';
COMMENT ON COLUMN email_push_permission.update_user IS '更新人';
COMMENT ON COLUMN email_push_permission.update_date IS '更新时间';
