package com.zqn.modeldata2.entity.email;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 邮件类型枚举
 * @date 2025/01/22 10:00
 */
public enum EmailTypeEnum {

    /**
     * 推送報價1版
     */
    QUOTE_V1("QUOTE_V1", "推送報價1版"),

    /**
     * 推送報價2版
     */
    QUOTE_V2("QUOTE_V2", "推送報價2版"),

    /**
     * 推送報價3版
     */
    QUOTE_V3("QUOTE_V3", "推送報價3版"),

    /**
     * 推送報價4版
     */
    QUOTE_V4("QUOTE_V4", "推送報價4版"),

    /**
     * 推送報價5版
     */
    QUOTE_V5("QUOTE_V5", "推送報價5版"),

    /**
     * 推送預估Z版
     */
    ESTIMATE_Z("ESTIMATE_Z", "推送預估Z版"),

    /**
     * 推送預估ZZ版
     */
    ESTIMATE_ZZ("ESTIMATE_ZZ", "推送預估ZZ版"),

    /**
     * 推送P版
     */
    P_VERSION("P_VERSION", "推送P版"),

    /**
     * 更新版本邮件提醒
     */
    UPDATE_NOTIFY("UPDATE_NOTIFY", "更新版本邮件提醒");

    private final String code;
    private final String description;

    EmailTypeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据代码获取枚举
     *
     * @param code 代码
     * @return 枚举
     */
    public static EmailTypeEnum getByCode(String code) {
        for (EmailTypeEnum emailType : values()) {
            if (emailType.getCode().equals(code)) {
                return emailType;
            }
        }
        return null;
    }

    /**
     * 检查代码是否有效
     *
     * @param code 代码
     * @return 是否有效
     */
    public static boolean isValidCode(String code) {
        return getByCode(code) != null;
    }

    /**
     * 获取所有邮件类型代码
     *
     * @return 代码数组
     */
    public static String[] getAllCodes() {
        EmailTypeEnum[] values = values();
        String[] codes = new String[values.length];
        for (int i = 0; i < values.length; i++) {
            codes[i] = values[i].getCode();
        }
        return codes;
    }

    /**
     * 根据报价版本获取邮件类型
     *
     * @param version 版本 (V1, V2, V3, V4, V5)
     * @return 邮件类型
     */
    public static EmailTypeEnum getQuoteEmailType(String version) {
        switch (version.toUpperCase()) {
            case "V1":
                return QUOTE_V1;
            case "V2":
                return QUOTE_V2;
            case "V3":
                return QUOTE_V3;
            case "V4":
                return QUOTE_V4;
            case "V5":
                return QUOTE_V5;
            default:
                return null;
        }
    }

    /**
     * 根据预估版本获取邮件类型
     *
     * @param version 版本 (Z, ZZ)
     * @return 邮件类型
     */
    public static EmailTypeEnum getEstimateEmailType(String version) {
        switch (version.toUpperCase()) {
            case "Z":
                return ESTIMATE_Z;
            case "ZZ":
                return ESTIMATE_ZZ;
            default:
                return null;
        }
    }

    /**
     * 检查是否为报价类型邮件
     *
     * @return 是否为报价类型
     */
    public boolean isQuoteType() {
        return this == QUOTE_V1 || this == QUOTE_V2 || this == QUOTE_V3 || this == QUOTE_V4 || this == QUOTE_V5;
    }

    /**
     * 检查是否为预估类型邮件
     *
     * @return 是否为预估类型
     */
    public boolean isEstimateType() {
        return this == ESTIMATE_Z || this == ESTIMATE_ZZ;
    }

    /**
     * 检查是否为P版类型邮件
     *
     * @return 是否为P版类型
     */
    public boolean isPVersionType() {
        return this == P_VERSION;
    }

    /**
     * 检查是否为更新通知类型邮件
     *
     * @return 是否为更新通知类型
     */
    public boolean isUpdateNotifyType() {
        return this == UPDATE_NOTIFY;
    }
}
