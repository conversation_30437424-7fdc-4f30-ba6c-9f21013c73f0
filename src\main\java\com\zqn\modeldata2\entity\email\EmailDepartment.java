package com.zqn.modeldata2.entity.email;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 邮件推送部门实体类
 * <AUTHOR> Assistant
 * @date 2025-01-22
 */
@Data
public class EmailDepartment {
    
    /** 部门ID */
    private String deptId;
    
    /** 部门名称 */
    private String deptName;
    
    /** 部门描述 */
    private String deptDesc;
    
    /** 状态 */
    private String status;
    
    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    
    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    
    /**
     * 检查部门是否启用
     * @return 是否启用
     */
    public boolean isEnabled() {
        return "Y".equals(status);
    }
}
