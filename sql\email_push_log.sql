-- 邮件推送记录表
CREATE TABLE email_push_log (
    id NUMBER(10) PRIMARY KEY,                   -- 主键ID
    push_type VARCHAR2(50) NOT NULL,             -- 推送类型 (QUOTE_V1,QUOTE_V2,QUOTE_V3,QUOTE_V4,QUOTE_V5,ESTIMATE_Z,ESTIMATE_ZZ,P_VERSION,UPDATE_NOTIFY)
    business_id VARCHAR2(100),                   -- 业务ID（如订单号、型体号等）
    business_type VARCHAR2(50),                  -- 业务类型（如ORDER、MODEL等）
    
    -- 收件人信息
    recipient_user_no VARCHAR2(20),              -- 收件人工号
    recipient_name VARCHAR2(100),                -- 收件人姓名
    recipient_email VARCHAR2(200) NOT NULL,      -- 收件人邮箱
    recipient_dept VARCHAR2(100),                -- 收件人部门
    
    -- 邮件内容
    email_subject VARCHAR2(500) NOT NULL,        -- 邮件主题
    email_content CLOB,                          -- 邮件内容
    email_attachments VARCHAR2(2000),            -- 附件路径（多个用逗号分隔）
    
    -- 推送状态
    push_status VARCHAR2(20) DEFAULT 'PENDING',  -- 推送状态 (PENDING-待发送, SENDING-发送中, SUCCESS-成功, FAILED-失败)
    send_time DATE,                              -- 发送时间
    success_time DATE,                           -- 成功时间
    retry_count NUMBER(2) DEFAULT 0,             -- 重试次数
    error_message VARCHAR2(1000),                -- 错误信息
    
    -- 业务相关信息
    brand_no VARCHAR2(20),                       -- 品牌
    model_no VARCHAR2(50),                       -- 型体
    ord_no VARCHAR2(50),                         -- 订单号
    version_info VARCHAR2(100),                  -- 版本信息
    
    -- 审计字段
    create_user VARCHAR2(20),                    -- 创建人
    create_date DATE DEFAULT SYSDATE,           -- 创建时间
    update_user VARCHAR2(20),                   -- 更新人
    update_date DATE DEFAULT SYSDATE,           -- 更新时间
    
    -- 约束
    CONSTRAINT chk_push_status CHECK (push_status IN ('PENDING', 'SENDING', 'SUCCESS', 'FAILED')),
    CONSTRAINT chk_push_type CHECK (push_type IN ('QUOTE_V1', 'QUOTE_V2', 'QUOTE_V3', 'QUOTE_V4', 'QUOTE_V5', 
                                                   'ESTIMATE_Z', 'ESTIMATE_ZZ', 'P_VERSION', 'UPDATE_NOTIFY'))
);

-- 创建索引
CREATE INDEX idx_email_log_user_no ON email_push_log(recipient_user_no);
CREATE INDEX idx_email_log_email ON email_push_log(recipient_email);
CREATE INDEX idx_email_log_status ON email_push_log(push_status);
CREATE INDEX idx_email_log_type ON email_push_log(push_type);
CREATE INDEX idx_email_log_business ON email_push_log(business_id, business_type);
CREATE INDEX idx_email_log_create_date ON email_push_log(create_date);
CREATE INDEX idx_email_log_brand_model ON email_push_log(brand_no, model_no);

-- 创建序列
CREATE SEQUENCE seq_email_push_log START WITH 1 INCREMENT BY 1;

-- 添加注释
COMMENT ON TABLE email_push_log IS '邮件推送记录表';
COMMENT ON COLUMN email_push_log.id IS '主键ID';
COMMENT ON COLUMN email_push_log.push_type IS '推送类型';
COMMENT ON COLUMN email_push_log.business_id IS '业务ID（如订单号、型体号等）';
COMMENT ON COLUMN email_push_log.business_type IS '业务类型（如ORDER、MODEL等）';
COMMENT ON COLUMN email_push_log.recipient_user_no IS '收件人工号';
COMMENT ON COLUMN email_push_log.recipient_name IS '收件人姓名';
COMMENT ON COLUMN email_push_log.recipient_email IS '收件人邮箱';
COMMENT ON COLUMN email_push_log.recipient_dept IS '收件人部门';
COMMENT ON COLUMN email_push_log.email_subject IS '邮件主题';
COMMENT ON COLUMN email_push_log.email_content IS '邮件内容';
COMMENT ON COLUMN email_push_log.email_attachments IS '附件路径（多个用逗号分隔）';
COMMENT ON COLUMN email_push_log.push_status IS '推送状态 (PENDING-待发送, SENDING-发送中, SUCCESS-成功, FAILED-失败)';
COMMENT ON COLUMN email_push_log.send_time IS '发送时间';
COMMENT ON COLUMN email_push_log.success_time IS '成功时间';
COMMENT ON COLUMN email_push_log.retry_count IS '重试次数';
COMMENT ON COLUMN email_push_log.error_message IS '错误信息';
COMMENT ON COLUMN email_push_log.brand_no IS '品牌';
COMMENT ON COLUMN email_push_log.model_no IS '型体';
COMMENT ON COLUMN email_push_log.ord_no IS '订单号';
COMMENT ON COLUMN email_push_log.version_info IS '版本信息';
COMMENT ON COLUMN email_push_log.create_user IS '创建人';
COMMENT ON COLUMN email_push_log.create_date IS '创建时间';
COMMENT ON COLUMN email_push_log.update_user IS '更新人';
COMMENT ON COLUMN email_push_log.update_date IS '更新时间';
