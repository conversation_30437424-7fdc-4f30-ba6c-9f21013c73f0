package com.zqn.modeldata2.service;

import com.zqn.modeldata2.entity.email.EmailPushLog;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 邮件发送Service接口
 * @date 2025/01/22 10:00
 */
public interface EmailSendService {

    /**
     * 发送单个邮件
     *
     * @param log 邮件日志
     * @return 是否发送成功
     */
    boolean sendSingleEmail(EmailPushLog log);

    /**
     * 发送测试邮件
     *
     * @param toEmail 收件人邮箱
     * @param subject 邮件主题
     * @param content 邮件内容
     * @return 是否发送成功
     */
    boolean sendTestEmail(String toEmail, String subject, String content);

    /**
     * 测试SMTP连接
     *
     * @return 是否连接成功
     */
    boolean testSmtpConnection();

    /**
     * 验证邮箱地址格式
     *
     * @param email 邮箱地址
     * @return 是否有效
     */
    boolean isValidEmail(String email);
}
