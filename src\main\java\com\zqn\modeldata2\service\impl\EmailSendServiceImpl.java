package com.zqn.modeldata2.service.impl;

import com.zqn.modeldata2.entity.email.EmailPushLog;
import com.zqn.modeldata2.service.EmailPushConfigService;
import com.zqn.modeldata2.service.EmailSendService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.web.servlet.server.Session;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.mail.*;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeMessage;
import java.util.Map;
import java.util.Properties;
import java.util.logging.Logger;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 邮件发送Service实现类
 * @date 2025/01/22 10:00
 */
@Slf4j
@Service
public class EmailSendServiceImpl implements EmailSendService {

    @Resource
    private EmailPushConfigService emailPushConfigService;

    /**
     * 邮箱地址正则表达式
     */
    private static final String EMAIL_PATTERN = 
        "^[a-zA-Z0-9_+&*-]+(?:\\.[a-zA-Z0-9_+&*-]+)*@(?:[a-zA-Z0-9-]+\\.)+[a-zA-Z]{2,7}$";
    
    private static final Pattern pattern = Pattern.compile(EMAIL_PATTERN);

    @Override
    public boolean sendSingleEmail(EmailPushLog log) {
        try {
            if (log == null) {
                log.error("邮件日志为空");
                return false;
            }

            // 验证邮箱地址
            if (!isValidEmail(log.getToEmail())) {
                log.error("收件人邮箱地址无效: {}", log.getToEmail());
                return false;
            }

            // 获取SMTP配置
            Map<String, String> smtpConfig = emailPushConfigService.getSmtpConfig();
            if (!isSmtpConfigValid(smtpConfig)) {
                log.error("SMTP配置无效");
                return false;
            }

            // 创建邮件会话
            Session session = createEmailSession(smtpConfig);
            
            // 创建邮件消息
            MimeMessage message = createMimeMessage(session, log, smtpConfig);
            
            // 发送邮件
            Transport.send(message);
            
            log.info("邮件发送成功: {} -> {}", log.getFromEmail(), log.getToEmail());
            return true;
            
        } catch (Exception e) {
            log.error("发送邮件失败: {} -> {}", log.getFromEmail(), log.getToEmail(), e);
            return false;
        }
    }

    @Override
    public boolean sendTestEmail(String toEmail, String subject, String content) {
        try {
            if (!isValidEmail(toEmail)) {
                log.error("测试邮箱地址无效: {}", toEmail);
                return false;
            }

            // 获取SMTP配置
            Map<String, String> smtpConfig = emailPushConfigService.getSmtpConfig();
            if (!isSmtpConfigValid(smtpConfig)) {
                log.error("SMTP配置无效");
                return false;
            }

            // 创建邮件会话
            Session session = createEmailSession(smtpConfig);
            
            // 创建测试邮件
            MimeMessage message = new MimeMessage(session);
            message.setFrom(new InternetAddress(smtpConfig.get("username")));
            message.setRecipients(Message.RecipientType.TO, InternetAddress.parse(toEmail));
            message.setSubject(StringUtils.hasText(subject) ? subject : "邮件配置测试");
            message.setContent(StringUtils.hasText(content) ? content : "这是一封测试邮件，用于验证邮件配置是否正确。", "text/html;charset=utf-8");
            
            // 发送邮件
            Transport.send(message);
            
            log.info("测试邮件发送成功: {}", toEmail);
            return true;
            
        } catch (Exception e) {
            log.error("发送测试邮件失败: {}", toEmail, e);
            return false;
        }
    }

    @Override
    public boolean testSmtpConnection() {
        try {
            // 获取SMTP配置
            Map<String, String> smtpConfig = emailPushConfigService.getSmtpConfig();
            if (!isSmtpConfigValid(smtpConfig)) {
                log.error("SMTP配置无效");
                return false;
            }

            // 创建邮件会话
            Session session = createEmailSession(smtpConfig);
            
            // 测试连接
            Transport transport = session.getTransport("smtp");
            transport.connect(
                smtpConfig.get("host"),
                Integer.parseInt(smtpConfig.get("port")),
                smtpConfig.get("username"),
                smtpConfig.get("password")
            );
            transport.close();
            
            log.info("SMTP连接测试成功");
            return true;
            
        } catch (Exception e) {
            log.error("SMTP连接测试失败", e);
            return false;
        }
    }

    @Override
    public boolean isValidEmail(String email) {
        if (!StringUtils.hasText(email)) {
            return false;
        }
        return pattern.matcher(email).matches();
    }

    /**
     * 验证SMTP配置是否有效
     */
    private boolean isSmtpConfigValid(Map<String, String> smtpConfig) {
        if (smtpConfig == null || smtpConfig.isEmpty()) {
            return false;
        }
        
        return StringUtils.hasText(smtpConfig.get("host")) &&
               StringUtils.hasText(smtpConfig.get("port")) &&
               StringUtils.hasText(smtpConfig.get("username")) &&
               StringUtils.hasText(smtpConfig.get("password"));
    }

    /**
     * 创建邮件会话
     */
    private Session createEmailSession(Map<String, String> smtpConfig) {
        Properties props = new Properties();
        props.put("mail.smtp.host", smtpConfig.get("host"));
        props.put("mail.smtp.port", smtpConfig.get("port"));
        props.put("mail.smtp.auth", smtpConfig.getOrDefault("auth", "true"));
        props.put("mail.smtp.starttls.enable", smtpConfig.getOrDefault("starttls", "true"));
        
        // SSL配置
        if ("true".equals(smtpConfig.get("ssl"))) {
            props.put("mail.smtp.ssl.enable", "true");
            props.put("mail.smtp.socketFactory.class", "javax.net.ssl.SSLSocketFactory");
            props.put("mail.smtp.socketFactory.port", smtpConfig.get("port"));
        }

        // 创建认证器
        Authenticator authenticator = new Authenticator() {
            @Override
            protected PasswordAuthentication getPasswordAuthentication() {
                return new PasswordAuthentication(smtpConfig.get("username"), smtpConfig.get("password"));
            }
        };

        return Session.getInstance(props, authenticator);
    }

    /**
     * 创建邮件消息
     */
    private MimeMessage createMimeMessage(Session session, EmailPushLog log, Map<String, String> smtpConfig) throws MessagingException {
        MimeMessage message = new MimeMessage(session);
        
        // 设置发件人
        String fromEmail = StringUtils.hasText(log.getFromEmail()) ? log.getFromEmail() : smtpConfig.get("username");
        message.setFrom(new InternetAddress(fromEmail));
        
        // 设置收件人
        message.setRecipients(Message.RecipientType.TO, InternetAddress.parse(log.getToEmail()));
        
        // 设置抄送
        if (StringUtils.hasText(log.getCcEmail())) {
            message.setRecipients(Message.RecipientType.CC, InternetAddress.parse(log.getCcEmail()));
        }
        
        // 设置密送
        if (StringUtils.hasText(log.getBccEmail())) {
            message.setRecipients(Message.RecipientType.BCC, InternetAddress.parse(log.getBccEmail()));
        }
        
        // 设置主题
        message.setSubject(log.getSubject(), "UTF-8");
        
        // 设置内容
        message.setContent(log.getContent(), "text/html;charset=utf-8");
        
        return message;
    }
}
