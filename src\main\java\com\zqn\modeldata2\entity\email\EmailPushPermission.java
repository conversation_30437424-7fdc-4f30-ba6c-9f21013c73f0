package com.zqn.modeldata2.entity.email;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 邮件推送权限配置实体类
 * <AUTHOR> Assistant
 * @date 2025-01-22
 */
@Data
public class EmailPushPermission {
    
    /** 权限ID */
    private String permissionId;
    
    /** 用户工号 */
    private String userNo;
    
    /** 部门ID */
    private String deptId;
    
    /** 推送報價1版本權限 */
    private String quoteV1Permission;
    
    /** 推送報價2版本權限 */
    private String quoteV2Permission;
    
    /** 推送報價3版本權限 */
    private String quoteV3Permission;
    
    /** 推送報價4版本權限 */
    private String quoteV4Permission;
    
    /** 推送報價5版本權限 */
    private String quoteV5Permission;
    
    /** 推送預估Z版權限 */
    private String estimateZPermission;
    
    /** 推送P版權限 */
    private String pVersionPermission;
    
    /** 更新版本郵件提醒 */
    private String versionUpdateNotification;
    
    /** 邮箱地址 */
    private String emailAddress;
    
    /** 状态 */
    private String status;
    
    /** 备注 */
    private String remarks;
    
    /** 审核状态 */
    private String approvalStatus;
    
    /** 审核备注 */
    private String approvalRemarks;
    
    /** 创建用户 */
    private String createUser;
    
    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    
    /** 更新用户 */
    private String updateUser;
    
    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    
    // 关联字段 - 不存储在数据库中
    /** 用户姓名 */
    private String userName;
    
    /** 部门名称 */
    private String deptName;
    
    /**
     * 检查是否有指定类型的推送权限
     * @param pushType 推送类型
     * @return 是否有权限
     */
    public boolean hasPermission(String pushType) {
        switch (pushType) {
            case "QUOTE_V1":
                return "Y".equals(quoteV1Permission);
            case "QUOTE_V2":
                return "Y".equals(quoteV2Permission);
            case "QUOTE_V3":
                return "Y".equals(quoteV3Permission);
            case "QUOTE_V4":
                return "Y".equals(quoteV4Permission);
            case "QUOTE_V5":
                return "Y".equals(quoteV5Permission);
            case "ESTIMATE_Z":
                return "Y".equals(estimateZPermission);
            case "P_VERSION":
                return "Y".equals(pVersionPermission);
            case "VERSION_UPDATE":
                return "Y".equals(versionUpdateNotification);
            default:
                return false;
        }
    }
    
    /**
     * 检查是否有任何报价版本权限
     * @return 是否有报价权限
     */
    public boolean hasAnyQuotePermission() {
        return "Y".equals(quoteV1Permission) || 
               "Y".equals(quoteV2Permission) || 
               "Y".equals(quoteV3Permission) || 
               "Y".equals(quoteV4Permission) || 
               "Y".equals(quoteV5Permission);
    }
}
