package com.zqn.modeldata2.mapper;

import com.zqn.modeldata2.entity.email.EmailTemplate;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 邮件模板Mapper接口
 * @date 2025/01/22 10:00
 */
@Mapper
public interface EmailTemplateMapper {

    /**
     * 根据ID查询邮件模板
     *
     * @param id 模板ID
     * @return 模板信息
     */
    EmailTemplate selectById(@Param("id") Long id);

    /**
     * 根据模板代码查询邮件模板
     *
     * @param templateCode 模板代码
     * @return 模板信息
     */
    EmailTemplate selectByTemplateCode(@Param("templateCode") String templateCode);

    /**
     * 根据邮件类型查询默认模板
     *
     * @param emailType 邮件类型
     * @param language  语言
     * @return 模板信息
     */
    EmailTemplate selectDefaultByEmailType(@Param("emailType") String emailType, 
                                          @Param("language") String language);

    /**
     * 根据邮件类型查询所有模板
     *
     * @param emailType 邮件类型
     * @return 模板列表
     */
    List<EmailTemplate> selectByEmailType(@Param("emailType") String emailType);

    /**
     * 查询所有启用的模板
     *
     * @return 模板列表
     */
    List<EmailTemplate> selectAllEnabled();

    /**
     * 根据条件查询模板列表
     *
     * @param emailType    邮件类型
     * @param language     语言
     * @param status       状态
     * @param templateName 模板名称（模糊查询）
     * @return 模板列表
     */
    List<EmailTemplate> selectByCondition(@Param("emailType") String emailType,
                                         @Param("language") String language,
                                         @Param("status") String status,
                                         @Param("templateName") String templateName);

    /**
     * 插入邮件模板
     *
     * @param template 模板信息
     * @return 影响行数
     */
    int insert(EmailTemplate template);

    /**
     * 根据ID更新邮件模板
     *
     * @param template 模板信息
     * @return 影响行数
     */
    int updateById(EmailTemplate template);

    /**
     * 根据ID删除邮件模板
     *
     * @param id 模板ID
     * @return 影响行数
     */
    int deleteById(@Param("id") Long id);

    /**
     * 根据模板代码删除邮件模板
     *
     * @param templateCode 模板代码
     * @return 影响行数
     */
    int deleteByTemplateCode(@Param("templateCode") String templateCode);

    /**
     * 检查模板代码是否存在
     *
     * @param templateCode 模板代码
     * @param excludeId    排除的ID（用于更新时检查）
     * @return 数量
     */
    int checkTemplateCodeExists(@Param("templateCode") String templateCode, 
                               @Param("excludeId") Long excludeId);

    /**
     * 设置默认模板（先清除同类型的默认标识，再设置新的默认模板）
     *
     * @param emailType 邮件类型
     * @param language  语言
     * @param id        模板ID
     * @return 影响行数
     */
    int setDefaultTemplate(@Param("emailType") String emailType, 
                          @Param("language") String language, 
                          @Param("id") Long id);

    /**
     * 清除指定邮件类型和语言的默认模板标识
     *
     * @param emailType 邮件类型
     * @param language  语言
     * @return 影响行数
     */
    int clearDefaultTemplate(@Param("emailType") String emailType, 
                            @Param("language") String language);

    /**
     * 根据条件统计模板数量
     *
     * @param emailType    邮件类型
     * @param language     语言
     * @param status       状态
     * @param templateName 模板名称（模糊查询）
     * @return 数量
     */
    int countByCondition(@Param("emailType") String emailType,
                        @Param("language") String language,
                        @Param("status") String status,
                        @Param("templateName") String templateName);
}
