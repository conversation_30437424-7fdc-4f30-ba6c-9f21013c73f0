-- 邮件推送功能表结构设计
-- 作者: AI Assistant
-- 创建日期: 2025-01-22

-- 1. 部门表 (如果不存在的话)
-- 从现有代码看已有 bc_depart 表，可能需要扩展
CREATE TABLE IF NOT EXISTS email_departments (
    dept_id VARCHAR2(20) PRIMARY KEY,
    dept_name VARCHAR2(100) NOT NULL,
    dept_desc VARCHAR2(200),
    status CHAR(1) DEFAULT 'Y' CHECK (status IN ('Y', 'N')),
    create_time DATE DEFAULT SYSDATE,
    update_time DATE DEFAULT SYSDATE
);

-- 2. 邮件推送权限配置表
CREATE TABLE email_push_permissions (
    permission_id VARCHAR2(32) PRIMARY KEY,
    user_no VARCHAR2(20) NOT NULL,
    dept_id VARCHAR2(20),
    -- 推送報價1~5版本權限
    quote_v1_permission CHAR(1) DEFAULT 'N' CHECK (quote_v1_permission IN ('Y', 'N')),
    quote_v2_permission CHAR(1) DEFAULT 'N' CHECK (quote_v2_permission IN ('Y', 'N')),
    quote_v3_permission CHAR(1) DEFAULT 'N' CHECK (quote_v3_permission IN ('Y', 'N')),
    quote_v4_permission CHAR(1) DEFAULT 'N' CHECK (quote_v4_permission IN ('Y', 'N')),
    quote_v5_permission CHAR(1) DEFAULT 'N' CHECK (quote_v5_permission IN ('Y', 'N')),
    -- 推送預估Z版權限
    estimate_z_permission CHAR(1) DEFAULT 'N' CHECK (estimate_z_permission IN ('Y', 'N')),
    -- 推送P版權限
    p_version_permission CHAR(1) DEFAULT 'N' CHECK (p_version_permission IN ('Y', 'N')),
    -- 更新版本郵件提醒
    version_update_notification CHAR(1) DEFAULT 'N' CHECK (version_update_notification IN ('Y', 'N')),
    -- 邮箱地址
    email_address VARCHAR2(100),
    -- 状态
    status CHAR(1) DEFAULT 'Y' CHECK (status IN ('Y', 'N')),
    -- 备注
    remarks VARCHAR2(500),
    -- 审核状态 (针对特殊权限如海外工厂相关)
    approval_status VARCHAR2(20) DEFAULT 'APPROVED' CHECK (approval_status IN ('PENDING', 'APPROVED', 'REJECTED')),
    approval_remarks VARCHAR2(500),
    -- 创建和更新信息
    create_user VARCHAR2(20),
    create_time DATE DEFAULT SYSDATE,
    update_user VARCHAR2(20),
    update_time DATE DEFAULT SYSDATE,
    -- 外键约束
    CONSTRAINT fk_email_perm_user FOREIGN KEY (user_no) REFERENCES sy_user(user_no),
    CONSTRAINT fk_email_perm_dept FOREIGN KEY (dept_id) REFERENCES email_departments(dept_id)
);

-- 3. 邮件推送模板表
CREATE TABLE email_push_templates (
    template_id VARCHAR2(32) PRIMARY KEY,
    template_name VARCHAR2(100) NOT NULL,
    template_type VARCHAR2(20) NOT NULL, -- QUOTE_V1, QUOTE_V2, QUOTE_V3, QUOTE_V4, QUOTE_V5, ESTIMATE_Z, P_VERSION, VERSION_UPDATE
    subject_template VARCHAR2(200) NOT NULL,
    content_template CLOB NOT NULL,
    -- 是否启用
    is_active CHAR(1) DEFAULT 'Y' CHECK (is_active IN ('Y', 'N')),
    -- 创建和更新信息
    create_user VARCHAR2(20),
    create_time DATE DEFAULT SYSDATE,
    update_user VARCHAR2(20),
    update_time DATE DEFAULT SYSDATE
);

-- 4. 邮件推送记录表
CREATE TABLE email_push_records (
    record_id VARCHAR2(32) PRIMARY KEY,
    -- 推送类型
    push_type VARCHAR2(20) NOT NULL, -- QUOTE_V1, QUOTE_V2, QUOTE_V3, QUOTE_V4, QUOTE_V5, ESTIMATE_Z, P_VERSION, VERSION_UPDATE
    -- 关联业务数据
    business_id VARCHAR2(50), -- 可能是订单号、型体号等
    business_type VARCHAR2(20), -- ORDER, MODEL, etc.
    -- 收件人信息
    recipient_user_no VARCHAR2(20),
    recipient_email VARCHAR2(100) NOT NULL,
    recipient_name VARCHAR2(100),
    recipient_dept VARCHAR2(100),
    -- 邮件内容
    email_subject VARCHAR2(200) NOT NULL,
    email_content CLOB,
    -- 附件信息 (JSON格式存储附件列表)
    attachments CLOB,
    -- 发送状态
    send_status VARCHAR2(20) DEFAULT 'PENDING' CHECK (send_status IN ('PENDING', 'SENDING', 'SUCCESS', 'FAILED')),
    send_time DATE,
    error_message VARCHAR2(500),
    retry_count NUMBER(2) DEFAULT 0,
    -- 创建信息
    create_user VARCHAR2(20),
    create_time DATE DEFAULT SYSDATE,
    -- 索引字段
    INDEX idx_email_push_records_type (push_type),
    INDEX idx_email_push_records_user (recipient_user_no),
    INDEX idx_email_push_records_status (send_status),
    INDEX idx_email_push_records_time (create_time)
);

-- 5. 邮件推送规则表
CREATE TABLE email_push_rules (
    rule_id VARCHAR2(32) PRIMARY KEY,
    rule_name VARCHAR2(100) NOT NULL,
    push_type VARCHAR2(20) NOT NULL,
    -- 触发条件 (JSON格式)
    trigger_conditions CLOB,
    -- 是否启用
    is_active CHAR(1) DEFAULT 'Y' CHECK (is_active IN ('Y', 'N')),
    -- 优先级
    priority NUMBER(3) DEFAULT 100,
    -- 创建和更新信息
    create_user VARCHAR2(20),
    create_time DATE DEFAULT SYSDATE,
    update_user VARCHAR2(20),
    update_time DATE DEFAULT SYSDATE
);

-- 6. 邮件推送配置表 (系统级配置)
CREATE TABLE email_push_config (
    config_id VARCHAR2(32) PRIMARY KEY,
    config_key VARCHAR2(50) NOT NULL UNIQUE,
    config_value VARCHAR2(500),
    config_desc VARCHAR2(200),
    config_type VARCHAR2(20) DEFAULT 'STRING', -- STRING, NUMBER, BOOLEAN, JSON
    is_active CHAR(1) DEFAULT 'Y' CHECK (is_active IN ('Y', 'N')),
    create_user VARCHAR2(20),
    create_time DATE DEFAULT SYSDATE,
    update_user VARCHAR2(20),
    update_time DATE DEFAULT SYSDATE
);

-- 创建序列
CREATE SEQUENCE seq_email_push_permissions START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE seq_email_push_templates START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE seq_email_push_records START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE seq_email_push_rules START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE seq_email_push_config START WITH 1 INCREMENT BY 1;

-- 插入初始部门数据 (基于您提供的数据)
INSERT INTO email_departments (dept_id, dept_name, dept_desc) VALUES ('SOP', 'SOP', 'SOP部门');
INSERT INTO email_departments (dept_id, dept_name, dept_desc) VALUES ('CHEMICAL', '化工', '化工部门');
INSERT INTO email_departments (dept_id, dept_name, dept_desc) VALUES ('OUTSOURCING', '線外加工', '線外加工部门');
INSERT INTO email_departments (dept_id, dept_name, dept_desc) VALUES ('BOTTOM', '底部', '底部部门');
INSERT INTO email_departments (dept_id, dept_name, dept_desc) VALUES ('SURFACE', '面部', '面部部门');
INSERT INTO email_departments (dept_id, dept_name, dept_desc) VALUES ('IE', 'IE工程部', 'IE工程部门');

-- 插入初始邮件模板
INSERT INTO email_push_templates (template_id, template_name, template_type, subject_template, content_template, create_user) 
VALUES ('TPL001', '報價V1版本推送', 'QUOTE_V1', '報價V1版本通知 - {business_id}', '您好，{recipient_name}，<br/>報價V1版本已更新，请查收。<br/>业务编号：{business_id}<br/>更新时间：{update_time}', 'SYSTEM');

INSERT INTO email_push_templates (template_id, template_name, template_type, subject_template, content_template, create_user) 
VALUES ('TPL002', '預估Z版推送', 'ESTIMATE_Z', '預估Z版通知 - {business_id}', '您好，{recipient_name}，<br/>預估Z版已更新，请查收。<br/>业务编号：{business_id}<br/>更新时间：{update_time}', 'SYSTEM');

INSERT INTO email_push_templates (template_id, template_name, template_type, subject_template, content_template, create_user) 
VALUES ('TPL003', 'P版推送', 'P_VERSION', 'P版通知 - {business_id}', '您好，{recipient_name}，<br/>P版已更新，请查收。<br/>业务编号：{business_id}<br/>更新时间：{update_time}', 'SYSTEM');

INSERT INTO email_push_templates (template_id, template_name, template_type, subject_template, content_template, create_user) 
VALUES ('TPL004', '版本更新提醒', 'VERSION_UPDATE', '版本更新提醒 - {business_id}', '您好，{recipient_name}，<br/>系统版本已更新，请及时查看。<br/>业务编号：{business_id}<br/>更新时间：{update_time}', 'SYSTEM');

-- 插入系统配置
INSERT INTO email_push_config (config_id, config_key, config_value, config_desc, config_type, create_user) 
VALUES ('CFG001', 'SMTP_HOST', 'smtp.stella.com.hk', 'SMTP服务器地址', 'STRING', 'SYSTEM');

INSERT INTO email_push_config (config_id, config_key, config_value, config_desc, config_type, create_user) 
VALUES ('CFG002', 'SMTP_PORT', '587', 'SMTP端口', 'NUMBER', 'SYSTEM');

INSERT INTO email_push_config (config_id, config_key, config_value, config_desc, config_type, create_user) 
VALUES ('CFG003', 'EMAIL_FROM', '<EMAIL>', '系统发件人邮箱', 'STRING', 'SYSTEM');

INSERT INTO email_push_config (config_id, config_key, config_value, config_desc, config_type, create_user) 
VALUES ('CFG004', 'MAX_RETRY_COUNT', '3', '最大重试次数', 'NUMBER', 'SYSTEM');

INSERT INTO email_push_config (config_id, config_key, config_value, config_desc, config_type, create_user)
VALUES ('CFG005', 'EMAIL_ENABLED', 'Y', '是否启用邮件推送', 'BOOLEAN', 'SYSTEM');

-- 插入用户权限数据 (基于提供的Excel数据)
-- SOP部门
INSERT INTO email_push_permissions (permission_id, user_no, dept_id, estimate_z_permission, version_update_notification, email_address, approval_status, approval_remarks, create_user)
VALUES ('PERM001', '2310001100', 'SOP', 'Y', 'Y', '<EMAIL>', 'PENDING', '另约各海外工厂相关单位开会了解当前作业模式，再统一签核流程權限，待定', 'SYSTEM');

INSERT INTO email_push_permissions (permission_id, user_no, dept_id, estimate_z_permission, version_update_notification, email_address, create_user)
VALUES ('PERM002', '2411001100', 'SOP', 'Y', 'Y', '<EMAIL>', 'SYSTEM');

INSERT INTO email_push_permissions (permission_id, user_no, dept_id, estimate_z_permission, version_update_notification, email_address, create_user)
VALUES ('PERM003', '2407006100', 'SOP', 'Y', 'Y', '<EMAIL>', 'SYSTEM');

INSERT INTO email_push_permissions (permission_id, user_no, dept_id, estimate_z_permission, version_update_notification, email_address, create_user)
VALUES ('PERM004', '2009087403', 'SOP', 'Y', 'Y', '<EMAIL>', 'SYSTEM');

INSERT INTO email_push_permissions (permission_id, user_no, dept_id, estimate_z_permission, version_update_notification, email_address, create_user)
VALUES ('PERM005', '2009151203', 'SOP', 'Y', 'Y', '<EMAIL>', 'SYSTEM');

INSERT INTO email_push_permissions (permission_id, user_no, dept_id, estimate_z_permission, version_update_notification, email_address, create_user)
VALUES ('PERM006', '2406000603', 'SOP', 'Y', 'Y', '<EMAIL>', 'SYSTEM');

INSERT INTO email_push_permissions (permission_id, user_no, dept_id, estimate_z_permission, version_update_notification, email_address, create_user)
VALUES ('PERM007', '2406000703', 'SOP', 'Y', 'Y', '<EMAIL>', 'SYSTEM');

INSERT INTO email_push_permissions (permission_id, user_no, dept_id, estimate_z_permission, version_update_notification, email_address, create_user)
VALUES ('PERM008', '2407000403', 'SOP', 'Y', 'Y', '<EMAIL>', 'SYSTEM');

INSERT INTO email_push_permissions (permission_id, user_no, dept_id, estimate_z_permission, version_update_notification, email_address, create_user)
VALUES ('PERM009', '2501000103', 'SOP', 'Y', 'Y', '<EMAIL>', 'SYSTEM');

INSERT INTO email_push_permissions (permission_id, user_no, dept_id, estimate_z_permission, version_update_notification, email_address, create_user)
VALUES ('PERM010', '2503000303', 'SOP', 'Y', 'Y', '<EMAIL>', 'SYSTEM');

-- 化工部门
INSERT INTO email_push_permissions (permission_id, user_no, dept_id, version_update_notification, email_address, create_user)
VALUES ('PERM011', '2009135303', 'CHEMICAL', 'Y', '<EMAIL>', 'SYSTEM');

INSERT INTO email_push_permissions (permission_id, user_no, dept_id, version_update_notification, email_address, create_user)
VALUES ('PERM012', '2410000303', 'CHEMICAL', 'Y', '<EMAIL>', 'SYSTEM');

-- 線外加工部门
INSERT INTO email_push_permissions (permission_id, user_no, dept_id, version_update_notification, email_address, create_user)
VALUES ('PERM013', '2009070003', 'OUTSOURCING', 'Y', '<EMAIL>', 'SYSTEM');

INSERT INTO email_push_permissions (permission_id, user_no, dept_id, quote_v1_permission, version_update_notification, email_address, create_user)
VALUES ('PERM014', '2206000503', 'OUTSOURCING', 'Y', 'Y', '<EMAIL>', 'SYSTEM');

INSERT INTO email_push_permissions (permission_id, user_no, dept_id, quote_v1_permission, version_update_notification, email_address, create_user)
VALUES ('PERM015', '2308000303', 'OUTSOURCING', 'Y', 'Y', '<EMAIL>', 'SYSTEM');

COMMIT;
