-- 邮件推送功能完整建表脚本
-- 执行顺序：先执行此脚本创建表结构，再执行初始化数据脚本

-- 1. 邮件推送权限配置表
CREATE TABLE email_push_permission (
    id NUMBER(10) PRIMARY KEY,
    user_no VARCHAR2(20) NOT NULL,
    dept_name VARCHAR2(100),
    email VARCHAR2(200),
    push_quote_v1 CHAR(1) DEFAULT 'N',
    push_quote_v2 CHAR(1) DEFAULT 'N',
    push_quote_v3 CHAR(1) DEFAULT 'N',
    push_quote_v4 CHAR(1) DEFAULT 'N',
    push_quote_v5 CHAR(1) DEFAULT 'N',
    push_estimate_z CHAR(1) DEFAULT 'N',
    push_estimate_zz CHAR(1) DEFAULT 'N',
    push_p_version CHAR(1) DEFAULT 'N',
    update_email_notify CHAR(1) DEFAULT 'N',
    status CHAR(1) DEFAULT 'Y',
    remark VARCHAR2(500),
    create_user VARCHAR2(20),
    create_date DATE DEFAULT SYSDATE,
    update_user VARCHAR2(20),
    update_date DATE DEFAULT SYSDATE,
    CONSTRAINT chk_push_quote_v1 CHECK (push_quote_v1 IN ('Y', 'N', 'P')),
    CONSTRAINT chk_push_quote_v2 CHECK (push_quote_v2 IN ('Y', 'N', 'P')),
    CONSTRAINT chk_push_quote_v3 CHECK (push_quote_v3 IN ('Y', 'N', 'P')),
    CONSTRAINT chk_push_quote_v4 CHECK (push_quote_v4 IN ('Y', 'N', 'P')),
    CONSTRAINT chk_push_quote_v5 CHECK (push_quote_v5 IN ('Y', 'N', 'P')),
    CONSTRAINT chk_push_estimate_z CHECK (push_estimate_z IN ('Y', 'N', 'P')),
    CONSTRAINT chk_push_estimate_zz CHECK (push_estimate_zz IN ('Y', 'N', 'P')),
    CONSTRAINT chk_push_p_version CHECK (push_p_version IN ('Y', 'N', 'P')),
    CONSTRAINT chk_update_email_notify CHECK (update_email_notify IN ('Y', 'N', 'P')),
    CONSTRAINT chk_permission_status CHECK (status IN ('Y', 'N'))
);

CREATE INDEX idx_email_push_user_no ON email_push_permission(user_no);
CREATE INDEX idx_email_push_dept ON email_push_permission(dept_name);
CREATE INDEX idx_email_push_email ON email_push_permission(email);
CREATE SEQUENCE seq_email_push_permission START WITH 1 INCREMENT BY 1;

-- 2. 邮件推送记录表
CREATE TABLE email_push_log (
    id NUMBER(10) PRIMARY KEY,
    push_type VARCHAR2(50) NOT NULL,
    business_id VARCHAR2(100),
    business_type VARCHAR2(50),
    recipient_user_no VARCHAR2(20),
    recipient_name VARCHAR2(100),
    recipient_email VARCHAR2(200) NOT NULL,
    recipient_dept VARCHAR2(100),
    email_subject VARCHAR2(500) NOT NULL,
    email_content CLOB,
    email_attachments VARCHAR2(2000),
    push_status VARCHAR2(20) DEFAULT 'PENDING',
    send_time DATE,
    success_time DATE,
    retry_count NUMBER(2) DEFAULT 0,
    error_message VARCHAR2(1000),
    brand_no VARCHAR2(20),
    model_no VARCHAR2(50),
    ord_no VARCHAR2(50),
    version_info VARCHAR2(100),
    create_user VARCHAR2(20),
    create_date DATE DEFAULT SYSDATE,
    update_user VARCHAR2(20),
    update_date DATE DEFAULT SYSDATE,
    CONSTRAINT chk_push_status CHECK (push_status IN ('PENDING', 'SENDING', 'SUCCESS', 'FAILED')),
    CONSTRAINT chk_push_type CHECK (push_type IN ('QUOTE_V1', 'QUOTE_V2', 'QUOTE_V3', 'QUOTE_V4', 'QUOTE_V5', 
                                                   'ESTIMATE_Z', 'ESTIMATE_ZZ', 'P_VERSION', 'UPDATE_NOTIFY'))
);

CREATE INDEX idx_email_log_user_no ON email_push_log(recipient_user_no);
CREATE INDEX idx_email_log_email ON email_push_log(recipient_email);
CREATE INDEX idx_email_log_status ON email_push_log(push_status);
CREATE INDEX idx_email_log_type ON email_push_log(push_type);
CREATE INDEX idx_email_log_business ON email_push_log(business_id, business_type);
CREATE INDEX idx_email_log_create_date ON email_push_log(create_date);
CREATE INDEX idx_email_log_brand_model ON email_push_log(brand_no, model_no);
CREATE SEQUENCE seq_email_push_log START WITH 1 INCREMENT BY 1;

-- 3. 邮件模板配置表
CREATE TABLE email_template (
    id NUMBER(10) PRIMARY KEY,
    template_code VARCHAR2(50) NOT NULL UNIQUE,
    template_name VARCHAR2(200) NOT NULL,
    template_type VARCHAR2(50) NOT NULL,
    subject_template VARCHAR2(500) NOT NULL,
    content_template CLOB NOT NULL,
    template_variables VARCHAR2(2000),
    is_html CHAR(1) DEFAULT 'Y',
    priority NUMBER(1) DEFAULT 3,
    need_attachment CHAR(1) DEFAULT 'N',
    status CHAR(1) DEFAULT 'Y',
    remark VARCHAR2(500),
    create_user VARCHAR2(20),
    create_date DATE DEFAULT SYSDATE,
    update_user VARCHAR2(20),
    update_date DATE DEFAULT SYSDATE,
    CONSTRAINT chk_template_is_html CHECK (is_html IN ('Y', 'N')),
    CONSTRAINT chk_template_priority CHECK (priority IN (1, 2, 3)),
    CONSTRAINT chk_template_need_attachment CHECK (need_attachment IN ('Y', 'N')),
    CONSTRAINT chk_template_status CHECK (status IN ('Y', 'N')),
    CONSTRAINT chk_template_type CHECK (template_type IN ('QUOTE_V1', 'QUOTE_V2', 'QUOTE_V3', 'QUOTE_V4', 'QUOTE_V5', 
                                                          'ESTIMATE_Z', 'ESTIMATE_ZZ', 'P_VERSION', 'UPDATE_NOTIFY'))
);

CREATE INDEX idx_email_template_code ON email_template(template_code);
CREATE INDEX idx_email_template_type ON email_template(template_type);
CREATE INDEX idx_email_template_status ON email_template(status);
CREATE SEQUENCE seq_email_template START WITH 1 INCREMENT BY 1;

-- 4. 邮件推送配置表
CREATE TABLE email_push_config (
    id NUMBER(10) PRIMARY KEY,
    config_key VARCHAR2(100) NOT NULL UNIQUE,
    config_value VARCHAR2(1000),
    config_desc VARCHAR2(500),
    config_type VARCHAR2(50) DEFAULT 'STRING',
    status CHAR(1) DEFAULT 'Y',
    remark VARCHAR2(500),
    create_user VARCHAR2(20),
    create_date DATE DEFAULT SYSDATE,
    update_user VARCHAR2(20),
    update_date DATE DEFAULT SYSDATE,
    CONSTRAINT chk_config_status CHECK (status IN ('Y', 'N')),
    CONSTRAINT chk_config_type CHECK (config_type IN ('STRING', 'NUMBER', 'BOOLEAN', 'JSON'))
);

CREATE INDEX idx_email_config_key ON email_push_config(config_key);
CREATE INDEX idx_email_config_type ON email_push_config(config_type);
CREATE INDEX idx_email_config_status ON email_push_config(status);
CREATE SEQUENCE seq_email_push_config START WITH 1 INCREMENT BY 1;

-- 添加表注释
COMMENT ON TABLE email_push_permission IS '邮件推送权限配置表';
COMMENT ON TABLE email_push_log IS '邮件推送记录表';
COMMENT ON TABLE email_template IS '邮件模板配置表';
COMMENT ON TABLE email_push_config IS '邮件推送配置表';

COMMIT;
