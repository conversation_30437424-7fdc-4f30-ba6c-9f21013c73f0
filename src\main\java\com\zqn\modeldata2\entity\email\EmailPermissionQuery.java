package com.zqn.modeldata2.entity.email;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 邮件推送权限查询DTO
 * @date 2025/01/22 10:00
 */
@Data
public class EmailPermissionQuery implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 工号
     */
    private String userNo;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 邮箱地址
     */
    private String email;

    /**
     * 邮件类型 (QUOTE_V1, QUOTE_V2, QUOTE_V3, QUOTE_V4, QUOTE_V5, ESTIMATE_Z, ESTIMATE_ZZ, P_VERSION, UPDATE_NOTIFY)
     */
    private String emailType;

    /**
     * 权限状态 (Y-有权限, N-无权限, P-待定)
     */
    private String permissionStatus;

    /**
     * 状态 (Y-启用, N-禁用)
     */
    private String status;

    /**
     * 是否包含无邮箱地址的用户 (Y-包含, N-不包含)
     */
    private String includeNoEmail = "N";

    /**
     * 页码
     */
    private Integer pageNo = 1;

    /**
     * 页大小
     */
    private Integer pageSize = 10;
}
