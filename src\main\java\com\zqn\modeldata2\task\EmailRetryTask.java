package com.zqn.modeldata2.task;

import com.zqn.modeldata2.common.R;
import com.zqn.modeldata2.service.EmailPushService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 邮件重试定时任务
 * @date 2025/01/22 10:00
 */
@Slf4j
@Component
public class EmailRetryTask {

    @Resource
    private EmailPushService emailPushService;

    /**
     * 每30分钟执行一次邮件重试任务
     */
    @Scheduled(fixedRate = 30 * 60 * 1000) // 30分钟
    public void retryFailedEmails() {
        try {
            log.info("开始执行邮件重试任务");
            
            R<String> result = emailPushService.batchRetryFailedEmails();
            
            if (result.getCode() == 1) {
                log.info("邮件重试任务执行成功: {}", result.getMsg());
            } else {
                log.warn("邮件重试任务执行失败: {}", result.getMsg());
            }
            
        } catch (Exception e) {
            log.error("邮件重试任务执行异常", e);
        }
    }

    /**
     * 每天凌晨2点清理30天前的邮件日志
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void cleanHistoryLogs() {
        try {
            log.info("开始执行邮件日志清理任务");
            
            // 清理30天前的日志
            java.util.Calendar calendar = java.util.Calendar.getInstance();
            calendar.add(java.util.Calendar.DAY_OF_MONTH, -30);
            java.util.Date beforeDate = calendar.getTime();
            
            R<String> result = emailPushService.cleanHistoryLogs(beforeDate);
            
            if (result.getCode() == 1) {
                log.info("邮件日志清理任务执行成功: {}", result.getMsg());
            } else {
                log.warn("邮件日志清理任务执行失败: {}", result.getMsg());
            }
            
        } catch (Exception e) {
            log.error("邮件日志清理任务执行异常", e);
        }
    }
}
