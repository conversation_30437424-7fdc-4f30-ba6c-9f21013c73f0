package com.zqn.modeldata2.service;

import com.github.pagehelper.PageInfo;
import com.zqn.modeldata2.common.R;
import com.zqn.modeldata2.entity.email.EmailTemplate;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 邮件模板Service接口
 * @date 2025/01/22 10:00
 */
public interface EmailTemplateService {

    /**
     * 分页查询邮件模板列表
     *
     * @param pageNo       页码
     * @param pageSize     页大小
     * @param emailType    邮件类型
     * @param language     语言
     * @param status       状态
     * @param templateName 模板名称（模糊查询）
     * @return 分页结果
     */
    R<PageInfo<EmailTemplate>> queryTemplateList(int pageNo, int pageSize, String emailType, 
                                                 String language, String status, String templateName);

    /**
     * 根据ID查询邮件模板
     *
     * @param id 模板ID
     * @return 模板信息
     */
    R<EmailTemplate> getTemplateById(Long id);

    /**
     * 根据模板代码查询邮件模板
     *
     * @param templateCode 模板代码
     * @return 模板信息
     */
    R<EmailTemplate> getTemplateByCode(String templateCode);

    /**
     * 根据邮件类型获取默认模板
     *
     * @param emailType 邮件类型
     * @param language  语言
     * @return 模板信息
     */
    R<EmailTemplate> getDefaultTemplate(String emailType, String language);

    /**
     * 根据邮件类型查询所有模板
     *
     * @param emailType 邮件类型
     * @return 模板列表
     */
    R<List<EmailTemplate>> getTemplatesByEmailType(String emailType);

    /**
     * 查询所有启用的模板
     *
     * @return 模板列表
     */
    R<List<EmailTemplate>> getAllEnabledTemplates();

    /**
     * 新增邮件模板
     *
     * @param template 模板信息
     * @return 操作结果
     */
    R<String> addTemplate(EmailTemplate template);

    /**
     * 更新邮件模板
     *
     * @param template 模板信息
     * @return 操作结果
     */
    R<String> updateTemplate(EmailTemplate template);

    /**
     * 删除邮件模板
     *
     * @param id 模板ID
     * @return 操作结果
     */
    R<String> deleteTemplate(Long id);

    /**
     * 设置默认模板
     *
     * @param id 模板ID
     * @return 操作结果
     */
    R<String> setDefaultTemplate(Long id);

    /**
     * 渲染邮件模板
     *
     * @param templateCode 模板代码
     * @param variables    变量Map
     * @return 渲染结果（包含主题和内容）
     */
    R<Map<String, String>> renderTemplate(String templateCode, Map<String, Object> variables);

    /**
     * 渲染邮件模板（根据邮件类型获取默认模板）
     *
     * @param emailType 邮件类型
     * @param language  语言
     * @param variables 变量Map
     * @return 渲染结果（包含主题和内容）
     */
    R<Map<String, String>> renderTemplateByType(String emailType, String language, Map<String, Object> variables);

    /**
     * 验证模板语法
     *
     * @param subjectTemplate 主题模板
     * @param contentTemplate 内容模板
     * @param variables       测试变量
     * @return 验证结果
     */
    R<String> validateTemplate(String subjectTemplate, String contentTemplate, Map<String, Object> variables);

    /**
     * 复制模板
     *
     * @param sourceId   源模板ID
     * @param newCode    新模板代码
     * @param newName    新模板名称
     * @param createUser 创建人
     * @return 操作结果
     */
    R<String> copyTemplate(Long sourceId, String newCode, String newName, String createUser);

    /**
     * 导入模板
     *
     * @param templates 模板列表
     * @return 操作结果
     */
    R<String> importTemplates(List<EmailTemplate> templates);

    /**
     * 导出模板
     *
     * @param emailType 邮件类型（可选）
     * @param language  语言（可选）
     * @return 模板列表
     */
    R<List<EmailTemplate>> exportTemplates(String emailType, String language);
}
