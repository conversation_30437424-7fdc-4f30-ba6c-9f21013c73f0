-- 邮件推送功能初始化数据脚本
-- 根据您提供的Excel数据初始化权限配置

-- SOP部门数据
INSERT INTO email_push_permission (id, user_no, dept_name, email, push_estimate_z, push_estimate_zz, push_p_version, update_email_notify, create_user) VALUES 
(seq_email_push_permission.nextval, '2310001100', 'SOP', '<EMAIL>', 'Y', 'Y', 'P', 'Y', 'SYSTEM');

INSERT INTO email_push_permission (id, user_no, dept_name, email, push_estimate_z, push_estimate_zz, push_p_version, update_email_notify, create_user) VALUES 
(seq_email_push_permission.nextval, '2411001100', 'SOP', '<EMAIL>', 'Y', 'Y', 'N', 'Y', 'SYSTEM');

INSERT INTO email_push_permission (id, user_no, dept_name, email, push_estimate_z, push_estimate_zz, push_p_version, update_email_notify, create_user) VALUES 
(seq_email_push_permission.nextval, '2407006100', 'SOP', '<EMAIL>', 'Y', 'Y', 'N', 'Y', 'SYSTEM');

INSERT INTO email_push_permission (id, user_no, dept_name, email, push_estimate_z, push_estimate_zz, push_p_version, update_email_notify, create_user) VALUES 
(seq_email_push_permission.nextval, '2009087403', 'SOP', '<EMAIL>', 'Y', 'Y', 'N', 'Y', 'SYSTEM');

INSERT INTO email_push_permission (id, user_no, dept_name, email, push_estimate_z, push_estimate_zz, push_p_version, update_email_notify, create_user) VALUES 
(seq_email_push_permission.nextval, '2009151203', 'SOP', '<EMAIL>', 'Y', 'Y', 'N', 'Y', 'SYSTEM');

INSERT INTO email_push_permission (id, user_no, dept_name, email, push_estimate_z, push_estimate_zz, push_p_version, update_email_notify, create_user) VALUES 
(seq_email_push_permission.nextval, '2406000603', 'SOP', '<EMAIL>', 'Y', 'Y', 'N', 'Y', 'SYSTEM');

INSERT INTO email_push_permission (id, user_no, dept_name, email, push_estimate_z, push_estimate_zz, push_p_version, update_email_notify, create_user) VALUES 
(seq_email_push_permission.nextval, '2406000703', 'SOP', '<EMAIL>', 'Y', 'Y', 'N', 'Y', 'SYSTEM');

INSERT INTO email_push_permission (id, user_no, dept_name, email, push_estimate_z, push_estimate_zz, push_p_version, update_email_notify, create_user) VALUES 
(seq_email_push_permission.nextval, '2407000403', 'SOP', '<EMAIL>', 'Y', 'Y', 'N', 'Y', 'SYSTEM');

INSERT INTO email_push_permission (id, user_no, dept_name, email, push_estimate_z, push_estimate_zz, push_p_version, update_email_notify, create_user) VALUES 
(seq_email_push_permission.nextval, '2501000103', 'SOP', '<EMAIL>', 'Y', 'Y', 'N', 'Y', 'SYSTEM');

INSERT INTO email_push_permission (id, user_no, dept_name, email, push_estimate_z, push_estimate_zz, push_p_version, update_email_notify, create_user) VALUES 
(seq_email_push_permission.nextval, '2503000303', 'SOP', '<EMAIL>', 'Y', 'Y', 'N', 'Y', 'SYSTEM');

-- 化工部门数据
INSERT INTO email_push_permission (id, user_no, dept_name, email, push_estimate_z, push_estimate_zz, push_p_version, update_email_notify, create_user) VALUES 
(seq_email_push_permission.nextval, '2009135303', '化工', '<EMAIL>', 'N', 'N', 'N', 'Y', 'SYSTEM');

INSERT INTO email_push_permission (id, user_no, dept_name, email, push_estimate_z, push_estimate_zz, push_p_version, update_email_notify, create_user) VALUES 
(seq_email_push_permission.nextval, '2410000303', '化工', '<EMAIL>', 'N', 'N', 'N', 'Y', 'SYSTEM');

-- 線外加工部门数据
INSERT INTO email_push_permission (id, user_no, dept_name, email, push_estimate_z, push_estimate_zz, push_p_version, update_email_notify, create_user) VALUES 
(seq_email_push_permission.nextval, '2009070003', '線外加工', '<EMAIL>', 'N', 'N', 'N', 'Y', 'SYSTEM');

INSERT INTO email_push_permission (id, user_no, dept_name, email, push_quote_v1, push_estimate_z, push_estimate_zz, push_p_version, update_email_notify, create_user) VALUES 
(seq_email_push_permission.nextval, '2206000503', '線外加工', '<EMAIL>', 'Y', 'N', 'N', 'N', 'Y', 'SYSTEM');

INSERT INTO email_push_permission (id, user_no, dept_name, email, push_quote_v1, push_estimate_z, push_estimate_zz, push_p_version, update_email_notify, create_user) VALUES 
(seq_email_push_permission.nextval, '2308000303', '線外加工', '<EMAIL>', 'Y', 'N', 'N', 'N', 'Y', 'SYSTEM');

-- 底部部门数据
INSERT INTO email_push_permission (id, user_no, dept_name, email, push_quote_v1, push_estimate_z, push_estimate_zz, push_p_version, update_email_notify, create_user) VALUES 
(seq_email_push_permission.nextval, '2009063903', '底部', '<EMAIL>', 'Y', 'N', 'N', 'N', 'Y', 'SYSTEM');

INSERT INTO email_push_permission (id, user_no, dept_name, email, push_quote_v1, push_estimate_z, push_estimate_zz, push_p_version, update_email_notify, remark, create_user) VALUES 
(seq_email_push_permission.nextval, '2009022203', '底部', '', 'Y', 'N', 'N', 'N', 'N', '暫無郵箱地址', 'SYSTEM');

INSERT INTO email_push_permission (id, user_no, dept_name, email, push_quote_v1, push_estimate_z, push_estimate_zz, push_p_version, update_email_notify, remark, create_user) VALUES 
(seq_email_push_permission.nextval, '2009062003', '底部', '', 'Y', 'N', 'N', 'N', 'N', '暫無郵箱地址', 'SYSTEM');

INSERT INTO email_push_permission (id, user_no, dept_name, email, push_quote_v1, push_estimate_z, push_estimate_zz, push_p_version, update_email_notify, remark, create_user) VALUES 
(seq_email_push_permission.nextval, '2009064203', '底部', '', 'Y', 'N', 'N', 'N', 'N', '暫無郵箱地址', 'SYSTEM');

INSERT INTO email_push_permission (id, user_no, dept_name, email, push_quote_v1, push_estimate_z, push_estimate_zz, push_p_version, update_email_notify, remark, create_user) VALUES 
(seq_email_push_permission.nextval, '2009066403', '底部', '', 'Y', 'N', 'N', 'N', 'N', '暫無郵箱地址', 'SYSTEM');

INSERT INTO email_push_permission (id, user_no, dept_name, email, push_quote_v1, push_estimate_z, push_estimate_zz, push_p_version, update_email_notify, create_user) VALUES 
(seq_email_push_permission.nextval, '2211000703', '底部', '<EMAIL>', 'Y', 'N', 'N', 'N', 'Y', 'SYSTEM');

INSERT INTO email_push_permission (id, user_no, dept_name, email, push_quote_v1, push_estimate_z, push_estimate_zz, push_p_version, update_email_notify, create_user) VALUES 
(seq_email_push_permission.nextval, '2009140703', '底部', '<EMAIL>', 'N', 'N', 'N', 'N', 'Y', 'SYSTEM');

INSERT INTO email_push_permission (id, user_no, dept_name, email, push_quote_v1, push_estimate_z, push_estimate_zz, push_p_version, update_email_notify, create_user) VALUES 
(seq_email_push_permission.nextval, '2009074303', '底部', '<EMAIL>', 'Y', 'N', 'N', 'N', 'Y', 'SYSTEM');

INSERT INTO email_push_permission (id, user_no, dept_name, email, push_quote_v1, push_estimate_z, push_estimate_zz, push_p_version, update_email_notify, create_user) VALUES 
(seq_email_push_permission.nextval, '2009073203', '底部', '<EMAIL>', 'Y', 'N', 'N', 'N', 'Y', 'SYSTEM');

INSERT INTO email_push_permission (id, user_no, dept_name, email, push_quote_v1, push_estimate_z, push_estimate_zz, push_p_version, update_email_notify, remark, create_user) VALUES 
(seq_email_push_permission.nextval, '2009073403', '底部', '', 'Y', 'N', 'N', 'N', 'N', '暫無郵箱地址', 'SYSTEM');

-- 面部部门数据
INSERT INTO email_push_permission (id, user_no, dept_name, email, push_quote_v1, push_estimate_z, push_estimate_zz, push_p_version, update_email_notify, create_user) VALUES 
(seq_email_push_permission.nextval, '2009012403', '面部', '<EMAIL>', 'Y', 'N', 'N', 'N', 'Y', 'SYSTEM');

INSERT INTO email_push_permission (id, user_no, dept_name, email, push_quote_v1, push_estimate_z, push_estimate_zz, push_p_version, update_email_notify, create_user) VALUES 
(seq_email_push_permission.nextval, '2009138003', '面部', '<EMAIL>', 'Y', 'N', 'N', 'N', 'Y', 'SYSTEM');

INSERT INTO email_push_permission (id, user_no, dept_name, email, push_quote_v1, push_estimate_z, push_estimate_zz, push_p_version, update_email_notify, create_user) VALUES 
(seq_email_push_permission.nextval, '2009051103', '面部', '<EMAIL>', 'Y', 'N', 'N', 'N', 'Y', 'SYSTEM');

INSERT INTO email_push_permission (id, user_no, dept_name, email, push_quote_v1, push_estimate_z, push_estimate_zz, push_p_version, update_email_notify, remark, create_user) VALUES 
(seq_email_push_permission.nextval, '2009119503', '面部', '', 'Y', 'N', 'N', 'N', 'N', '暫無郵箱地址', 'SYSTEM');

COMMIT;
