-- 邮件推送配置表
CREATE TABLE email_push_config (
    id NUMBER(10) PRIMARY KEY,                   -- 主键ID
    config_key VARCHAR2(100) NOT NULL UNIQUE,   -- 配置键
    config_value VARCHAR2(1000),                -- 配置值
    config_desc VARCHAR2(500),                  -- 配置描述
    config_type VARCHAR2(50) DEFAULT 'STRING',  -- 配置类型 (STRING, NUMBER, BOOLEAN, JSON)
    
    -- 状态控制
    status CHAR(1) DEFAULT 'Y',                 -- 状态 (Y-启用, N-禁用)
    remark VARCHAR2(500),                       -- 备注说明
    
    -- 审计字段
    create_user VARCHAR2(20),                   -- 创建人
    create_date DATE DEFAULT SYSDATE,          -- 创建时间
    update_user VARCHAR2(20),                  -- 更新人
    update_date DATE DEFAULT SYSDATE,          -- 更新时间
    
    -- 约束
    CONSTRAINT chk_config_status CHECK (status IN ('Y', 'N')),
    CONSTRAINT chk_config_type CHECK (config_type IN ('STRING', 'NUMBER', 'BOOLEAN', 'JSON'))
);

-- 创建索引
CREATE INDEX idx_email_config_key ON email_push_config(config_key);
CREATE INDEX idx_email_config_type ON email_push_config(config_type);
CREATE INDEX idx_email_config_status ON email_push_config(status);

-- 创建序列
CREATE SEQUENCE seq_email_push_config START WITH 1 INCREMENT BY 1;

-- 添加注释
COMMENT ON TABLE email_push_config IS '邮件推送配置表';
COMMENT ON COLUMN email_push_config.id IS '主键ID';
COMMENT ON COLUMN email_push_config.config_key IS '配置键';
COMMENT ON COLUMN email_push_config.config_value IS '配置值';
COMMENT ON COLUMN email_push_config.config_desc IS '配置描述';
COMMENT ON COLUMN email_push_config.config_type IS '配置类型 (STRING, NUMBER, BOOLEAN, JSON)';
COMMENT ON COLUMN email_push_config.status IS '状态 (Y-启用, N-禁用)';
COMMENT ON COLUMN email_push_config.remark IS '备注说明';
COMMENT ON COLUMN email_push_config.create_user IS '创建人';
COMMENT ON COLUMN email_push_config.create_date IS '创建时间';
COMMENT ON COLUMN email_push_config.update_user IS '更新人';
COMMENT ON COLUMN email_push_config.update_date IS '更新时间';

-- 插入默认配置数据
INSERT INTO email_push_config (id, config_key, config_value, config_desc, config_type, create_user) VALUES 
(seq_email_push_config.nextval, 'SMTP_HOST', 'smtp.stella.com.hk', 'SMTP服务器地址', 'STRING', 'SYSTEM');

INSERT INTO email_push_config (id, config_key, config_value, config_desc, config_type, create_user) VALUES 
(seq_email_push_config.nextval, 'SMTP_PORT', '587', 'SMTP服务器端口', 'NUMBER', 'SYSTEM');

INSERT INTO email_push_config (id, config_key, config_value, config_desc, config_type, create_user) VALUES 
(seq_email_push_config.nextval, 'SMTP_USERNAME', '<EMAIL>', 'SMTP用户名', 'STRING', 'SYSTEM');

INSERT INTO email_push_config (id, config_key, config_value, config_desc, config_type, create_user) VALUES 
(seq_email_push_config.nextval, 'SMTP_PASSWORD', '', 'SMTP密码', 'STRING', 'SYSTEM');

INSERT INTO email_push_config (id, config_key, config_value, config_desc, config_type, create_user) VALUES 
(seq_email_push_config.nextval, 'SMTP_SSL_ENABLE', 'true', '是否启用SSL', 'BOOLEAN', 'SYSTEM');

INSERT INTO email_push_config (id, config_key, config_value, config_desc, config_type, create_user) VALUES 
(seq_email_push_config.nextval, 'EMAIL_FROM', '<EMAIL>', '发件人邮箱', 'STRING', 'SYSTEM');

INSERT INTO email_push_config (id, config_key, config_value, config_desc, config_type, create_user) VALUES 
(seq_email_push_config.nextval, 'EMAIL_FROM_NAME', 'Stella系统', '发件人名称', 'STRING', 'SYSTEM');

INSERT INTO email_push_config (id, config_key, config_value, config_desc, config_type, create_user) VALUES 
(seq_email_push_config.nextval, 'MAX_RETRY_COUNT', '3', '最大重试次数', 'NUMBER', 'SYSTEM');

INSERT INTO email_push_config (id, config_key, config_value, config_desc, config_type, create_user) VALUES 
(seq_email_push_config.nextval, 'RETRY_INTERVAL', '300', '重试间隔（秒）', 'NUMBER', 'SYSTEM');

INSERT INTO email_push_config (id, config_key, config_value, config_desc, config_type, create_user) VALUES 
(seq_email_push_config.nextval, 'BATCH_SIZE', '50', '批量发送数量', 'NUMBER', 'SYSTEM');

COMMIT;
