package com.zqn.modeldata2.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.zqn.modeldata2.common.R;
import com.zqn.modeldata2.entity.email.*;
import com.zqn.modeldata2.mapper.EmailPushLogMapper;
import com.zqn.modeldata2.service.EmailPushConfigService;
import com.zqn.modeldata2.service.EmailPushPermissionService;
import com.zqn.modeldata2.service.EmailPushService;
import com.zqn.modeldata2.service.EmailSendService;
import com.zqn.modeldata2.service.EmailTemplateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 邮件推送Service实现类
 * @date 2025/01/22 10:00
 */
@Slf4j
@Service
public class EmailPushServiceImpl implements EmailPushService {

    @Resource
    private EmailPushLogMapper emailPushLogMapper;

    @Resource
    private EmailPushPermissionService emailPushPermissionService;

    @Resource
    private EmailTemplateService emailTemplateService;

    @Resource
    private EmailPushConfigService emailPushConfigService;

    @Resource
    private EmailSendService emailSendService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<String> sendEmail(EmailPushRequest request) {
        try {
            // 参数验证
            if (!EmailTypeEnum.isValidCode(request.getEmailType())) {
                return R.error("无效的邮件类型：" + request.getEmailType());
            }

            // 生成批次ID
            String batchId = generateBatchId();
            
            // 获取收件人列表
            List<EmailPushPermission> recipients = getRecipients(request);
            if (recipients.isEmpty()) {
                return R.error("未找到有权限的收件人");
            }

            // 获取邮件模板
            R<EmailTemplate> templateResult = emailTemplateService.getDefaultTemplate(request.getEmailType(), "zh_CN");
            if (templateResult.getCode() != 1 || templateResult.getData() == null) {
                return R.error("未找到邮件模板");
            }

            // 渲染邮件内容
            Map<String, String> renderedContent = renderEmailContent(templateResult.getData(), request);

            // 创建邮件日志
            List<EmailPushLog> logs = createEmailLogs(batchId, request, recipients, renderedContent);
            
            // 保存日志
            emailPushLogMapper.batchInsert(logs);

            // 发送邮件（如果是立即发送）
            if (Boolean.TRUE.equals(request.getImmediate())) {
                sendEmailsImmediately(logs);
            }

            return R.success("邮件发送请求已提交，批次ID：" + batchId);
        } catch (Exception e) {
            log.error("发送邮件失败", e);
            return R.error("发送邮件失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<String> batchSendEmail(List<EmailPushRequest> requests) {
        try {
            if (requests == null || requests.isEmpty()) {
                return R.error("邮件请求列表不能为空");
            }

            List<String> batchIds = new ArrayList<>();
            for (EmailPushRequest request : requests) {
                R<String> result = sendEmail(request);
                if (result.getCode() == 1) {
                    // 提取批次ID
                    String batchId = extractBatchIdFromMessage(result.getMsg());
                    if (StringUtils.hasText(batchId)) {
                        batchIds.add(batchId);
                    }
                }
            }

            return R.success("批量发送完成，共处理 " + requests.size() + " 个请求，成功 " + batchIds.size() + " 个");
        } catch (Exception e) {
            log.error("批量发送邮件失败", e);
            return R.error("批量发送邮件失败：" + e.getMessage());
        }
    }

    @Override
    public R<String> sendQuoteEmail(String quoteVersion, String businessId, String sendUser) {
        try {
            EmailTypeEnum emailType = EmailTypeEnum.getQuoteEmailType(quoteVersion);
            if (emailType == null) {
                return R.error("无效的报价版本：" + quoteVersion);
            }

            EmailPushRequest request = new EmailPushRequest();
            request.setEmailType(emailType.getCode());
            request.setBusinessId(businessId);
            request.setBusinessType("QUOTE");
            request.setSendUser(sendUser);
            request.setImmediate(true);

            // 设置模板变量
            Map<String, Object> variables = new HashMap<>();
            variables.put("businessId", businessId);
            variables.put("quoteVersion", quoteVersion);
            variables.put("currentDate", new Date());
            request.setTemplateVariables(variables);

            return sendEmail(request);
        } catch (Exception e) {
            log.error("发送报价邮件失败，quoteVersion: {}, businessId: {}", quoteVersion, businessId, e);
            return R.error("发送报价邮件失败：" + e.getMessage());
        }
    }

    @Override
    public R<String> sendEstimateEmail(String estimateVersion, String businessId, String sendUser) {
        try {
            EmailTypeEnum emailType = EmailTypeEnum.getEstimateEmailType(estimateVersion);
            if (emailType == null) {
                return R.error("无效的预估版本：" + estimateVersion);
            }

            EmailPushRequest request = new EmailPushRequest();
            request.setEmailType(emailType.getCode());
            request.setBusinessId(businessId);
            request.setBusinessType("ESTIMATE");
            request.setSendUser(sendUser);
            request.setImmediate(true);

            // 设置模板变量
            Map<String, Object> variables = new HashMap<>();
            variables.put("businessId", businessId);
            variables.put("estimateVersion", estimateVersion);
            variables.put("currentDate", new Date());
            request.setTemplateVariables(variables);

            return sendEmail(request);
        } catch (Exception e) {
            log.error("发送预估邮件失败，estimateVersion: {}, businessId: {}", estimateVersion, businessId, e);
            return R.error("发送预估邮件失败：" + e.getMessage());
        }
    }

    @Override
    public R<String> sendPVersionEmail(String businessId, String sendUser) {
        try {
            EmailPushRequest request = new EmailPushRequest();
            request.setEmailType(EmailTypeEnum.P_VERSION.getCode());
            request.setBusinessId(businessId);
            request.setBusinessType("P_VERSION");
            request.setSendUser(sendUser);
            request.setImmediate(true);

            // 设置模板变量
            Map<String, Object> variables = new HashMap<>();
            variables.put("businessId", businessId);
            variables.put("currentDate", new Date());
            request.setTemplateVariables(variables);

            return sendEmail(request);
        } catch (Exception e) {
            log.error("发送P版邮件失败，businessId: {}", businessId, e);
            return R.error("发送P版邮件失败：" + e.getMessage());
        }
    }

    @Override
    public R<String> sendUpdateNotifyEmail(String businessId, String sendUser) {
        try {
            EmailPushRequest request = new EmailPushRequest();
            request.setEmailType(EmailTypeEnum.UPDATE_NOTIFY.getCode());
            request.setBusinessId(businessId);
            request.setBusinessType("UPDATE_NOTIFY");
            request.setSendUser(sendUser);
            request.setImmediate(true);

            // 设置模板变量
            Map<String, Object> variables = new HashMap<>();
            variables.put("businessId", businessId);
            variables.put("currentDate", new Date());
            request.setTemplateVariables(variables);

            return sendEmail(request);
        } catch (Exception e) {
            log.error("发送更新通知邮件失败，businessId: {}", businessId, e);
            return R.error("发送更新通知邮件失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<String> retryFailedEmail(Long logId) {
        try {
            EmailPushLog log = emailPushLogMapper.selectById(logId);
            if (log == null) {
                return R.error("邮件日志不存在");
            }

            if (!EmailSendStatusEnum.FAILED.getCode().equals(log.getSendStatus())) {
                return R.error("只能重试失败的邮件");
            }

            // 检查重试次数
            if (log.getRetryCount() >= log.getMaxRetry()) {
                return R.error("已达到最大重试次数");
            }

            // 更新状态为重试中
            log.setSendStatus(EmailSendStatusEnum.RETRY.getCode());
            log.setRetryCount(log.getRetryCount() + 1);
            log.setUpdateDate(new Date());
            emailPushLogMapper.updateById(log);

            // 执行邮件发送
            boolean success = sendSingleEmail(log);
            
            // 更新发送结果
            log.setSendStatus(success ? EmailSendStatusEnum.SUCCESS.getCode() : EmailSendStatusEnum.FAILED.getCode());
            log.setSendTime(new Date());
            log.setUpdateDate(new Date());
            
            if (!success) {
                // 设置下次重试时间
                Calendar calendar = Calendar.getInstance();
                calendar.add(Calendar.MINUTE, 30); // 30分钟后重试
                log.setNextRetryTime(calendar.getTime());
            }
            
            emailPushLogMapper.updateById(log);

            return R.success(success ? "邮件重试发送成功" : "邮件重试发送失败");
        } catch (Exception e) {
            log.error("重试邮件失败，logId: {}", logId, e);
            return R.error("重试邮件失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<String> batchRetryFailedEmails() {
        try {
            List<EmailPushLog> retryLogs = emailPushLogMapper.selectRetryLogs();
            if (retryLogs.isEmpty()) {
                return R.success("没有需要重试的邮件");
            }

            int successCount = 0;
            for (EmailPushLog emailLog : retryLogs) {
                try {
                    R<String> result = retryFailedEmail(emailLog.getId());
                    if (result.getCode() == 1) {
                        successCount++;
                    }
                } catch (Exception e) {
                    log.error("重试邮件失败，logId: {}", emailLog.getId(), e);
                }
            }

            return R.success("批量重试完成，共处理 " + retryLogs.size() + " 个邮件，成功 " + successCount + " 个");
        } catch (Exception e) {
            log.error("批量重试邮件失败", e);
            return R.error("批量重试邮件失败：" + e.getMessage());
        }
    }

    @Override
    public R<PageInfo<EmailPushLog>> queryEmailLogs(int pageNo, int pageSize, String emailType, 
                                                    String sendStatus, String userNo, String businessId, 
                                                    Date startDate, Date endDate) {
        try {
            PageHelper.startPage(pageNo, pageSize);
            List<EmailPushLog> list = emailPushLogMapper.selectByCondition(emailType, sendStatus, userNo, businessId, startDate, endDate);
            PageInfo<EmailPushLog> pageInfo = new PageInfo<>(list);
            return R.success(pageInfo);
        } catch (Exception e) {
            log.error("查询邮件日志失败", e);
            return R.error("查询邮件日志失败：" + e.getMessage());
        }
    }

    @Override
    public R<List<EmailPushLog>> getEmailLogsByBatchId(String batchId) {
        try {
            List<EmailPushLog> logs = emailPushLogMapper.selectByBatchId(batchId);
            return R.success(logs);
        } catch (Exception e) {
            log.error("根据批次ID查询邮件日志失败，batchId: {}", batchId, e);
            return R.error("查询邮件日志失败：" + e.getMessage());
        }
    }

    @Override
    public R<List<EmailPushLog>> getEmailLogsByBusinessId(String businessId) {
        try {
            List<EmailPushLog> logs = emailPushLogMapper.selectByBusinessId(businessId);
            return R.success(logs);
        } catch (Exception e) {
            log.error("根据业务ID查询邮件日志失败，businessId: {}", businessId, e);
            return R.error("查询邮件日志失败：" + e.getMessage());
        }
    }

    @Override
    public R<List<EmailPushLog>> getEmailSendStatistics(String emailType, Date startDate, Date endDate) {
        try {
            List<EmailPushLog> statistics = emailPushLogMapper.selectSendStatistics(emailType, startDate, endDate);
            return R.success(statistics);
        } catch (Exception e) {
            log.error("获取邮件发送统计失败", e);
            return R.error("获取邮件发送统计失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<String> cleanHistoryLogs(Date beforeDate) {
        try {
            int deletedCount = emailPushLogMapper.deleteBeforeDate(beforeDate);
            return R.success("清理历史日志成功，共删除 " + deletedCount + " 条记录");
        } catch (Exception e) {
            log.error("清理历史日志失败", e);
            return R.error("清理历史日志失败：" + e.getMessage());
        }
    }

    @Override
    public R<String> testEmailConfig(String testEmail) {
        try {
            // 使用邮件发送服务测试配置
            boolean success = emailSendService.sendTestEmail(testEmail, "邮件配置测试", "这是一封测试邮件，用于验证邮件配置是否正确。");
            if (success) {
                return R.success("邮件配置测试成功，测试邮件已发送到：" + testEmail);
            } else {
                return R.error("邮件配置测试失败，请检查SMTP配置");
            }
        } catch (Exception e) {
            log.error("测试邮件配置失败", e);
            return R.error("测试邮件配置失败：" + e.getMessage());
        }
    }

    /**
     * 生成批次ID
     */
    private String generateBatchId() {
        return "BATCH_" + System.currentTimeMillis() + "_" + UUID.randomUUID().toString().substring(0, 8);
    }

    /**
     * 获取收件人列表
     */
    private List<EmailPushPermission> getRecipients(EmailPushRequest request) {
        if (request.getUserNos() != null && !request.getUserNos().isEmpty()) {
            // 指定收件人
            R<List<EmailPushPermission>> result = emailPushPermissionService.getPermissionsByUserNos(request.getUserNos());
            return result.getCode() == 1 ? result.getData() : new ArrayList<>();
        } else {
            // 根据权限获取收件人
            return emailPushPermissionService.getValidRecipients(request.getEmailType());
        }
    }

    /**
     * 渲染邮件内容
     */
    private Map<String, String> renderEmailContent(EmailTemplate template, EmailPushRequest request) {
        Map<String, String> result = new HashMap<>();
        
        // 使用自定义内容或模板内容
        String subject = StringUtils.hasText(request.getCustomSubject()) ? 
                request.getCustomSubject() : template.getSubjectTemplate();
        String content = StringUtils.hasText(request.getCustomContent()) ? 
                request.getCustomContent() : template.getContentTemplate();

        // 简单的变量替换（后续可以集成模板引擎）
        if (request.getTemplateVariables() != null) {
            for (Map.Entry<String, Object> entry : request.getTemplateVariables().entrySet()) {
                String placeholder = "${" + entry.getKey() + "}";
                String value = entry.getValue() != null ? entry.getValue().toString() : "";
                subject = subject.replace(placeholder, value);
                content = content.replace(placeholder, value);
            }
        }

        result.put("subject", subject);
        result.put("content", content);
        return result;
    }

    /**
     * 创建邮件日志
     */
    private List<EmailPushLog> createEmailLogs(String batchId, EmailPushRequest request, 
                                              List<EmailPushPermission> recipients, 
                                              Map<String, String> renderedContent) {
        List<EmailPushLog> logs = new ArrayList<>();
        Date now = new Date();
        
        for (EmailPushPermission recipient : recipients) {
            EmailPushLog log = new EmailPushLog();
            log.setBatchId(batchId);
            log.setEmailType(request.getEmailType());
            log.setSubject(renderedContent.get("subject"));
            log.setContent(renderedContent.get("content"));
            log.setFromEmail(emailPushConfigService.getConfigValue("EMAIL_FROM", "<EMAIL>"));
            log.setToEmail(recipient.getEmail());
            log.setUserNo(recipient.getUserNo());
            log.setDeptName(recipient.getDeptName());
            log.setBusinessId(request.getBusinessId());
            log.setBusinessType(request.getBusinessType());
            log.setSendStatus(EmailSendStatusEnum.PENDING.getCode());
            log.setRetryCount(0);
            log.setMaxRetry(Integer.parseInt(emailPushConfigService.getConfigValue("MAX_RETRY_COUNT", "3")));
            log.setCreateUser(request.getSendUser());
            log.setCreateDate(now);
            log.setUpdateDate(now);
            
            logs.add(log);
        }
        
        return logs;
    }

    /**
     * 立即发送邮件
     */
    private void sendEmailsImmediately(List<EmailPushLog> logs) {
        for (EmailPushLog emailLog : logs) {
            try {
                boolean success = sendSingleEmail(emailLog);
                emailLog.setSendStatus(success ? EmailSendStatusEnum.SUCCESS.getCode() : EmailSendStatusEnum.FAILED.getCode());
                emailLog.setSendTime(new Date());
                emailLog.setUpdateDate(new Date());
                emailPushLogMapper.updateById(emailLog);
            } catch (Exception e) {
                log.error("发送邮件失败，logId: {}", emailLog.getId(), e);
                emailLog.setSendStatus(EmailSendStatusEnum.FAILED.getCode());
                emailLog.setErrorMessage(e.getMessage());
                emailLog.setUpdateDate(new Date());
                emailPushLogMapper.updateById(emailLog);
            }
        }
    }

    /**
     * 发送单个邮件
     */
    private boolean sendSingleEmail(EmailPushLog emailLog) {
        try {
            log.info("开始发送邮件：{} -> {}", emailLog.getFromEmail(), emailLog.getToEmail());

            // 使用邮件发送服务发送邮件
            boolean success = emailSendService.sendSingleEmail(emailLog);

            if (success) {
                log.info("邮件发送成功：{} -> {}", emailLog.getFromEmail(), emailLog.getToEmail());
            } else {
                log.warn("邮件发送失败：{} -> {}", emailLog.getFromEmail(), emailLog.getToEmail());
            }

            return success;
        } catch (Exception e) {
            log.error("发送邮件异常：{} -> {}", emailLog.getFromEmail(), emailLog.getToEmail(), e);
            return false;
        }
    }

    /**
     * 从消息中提取批次ID
     */
    private String extractBatchIdFromMessage(String message) {
        if (StringUtils.hasText(message) && message.contains("批次ID：")) {
            return message.substring(message.indexOf("批次ID：") + 4);
        }
        return null;
    }
}
