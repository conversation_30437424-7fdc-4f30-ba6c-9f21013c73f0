package com.zqn.modeldata2.controller;

import com.github.pagehelper.PageInfo;
import com.zqn.modeldata2.common.R;
import com.zqn.modeldata2.entity.email.EmailPushLog;
import com.zqn.modeldata2.entity.email.EmailPushRequest;
import com.zqn.modeldata2.service.EmailPushService;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 邮件推送Controller
 * @date 2025/01/22 10:00
 */
@RestController
@RequestMapping("/emailPush")
@Validated
public class EmailPushController {

    @Resource
    private EmailPushService emailPushService;

    /**
     * 发送邮件
     *
     * @param request 邮件推送请求
     * @return 操作结果
     */
    @PostMapping("/send")
    public R<String> sendEmail(@RequestBody @Validated EmailPushRequest request) {
        return emailPushService.sendEmail(request);
    }

    /**
     * 批量发送邮件
     *
     * @param requests 邮件推送请求列表
     * @return 操作结果
     */
    @PostMapping("/batchSend")
    public R<String> batchSendEmail(@RequestBody List<EmailPushRequest> requests) {
        return emailPushService.batchSendEmail(requests);
    }

    /**
     * 发送报价邮件
     *
     * @param quoteVersion 报价版本 (V1, V2, V3, V4, V5)
     * @param businessId   业务ID
     * @param sendUser     发送人
     * @return 操作结果
     */
    @PostMapping("/sendQuote")
    public R<String> sendQuoteEmail(@RequestParam("quoteVersion") @NotBlank(message = "报价版本不能为空") String quoteVersion,
                                   @RequestParam("businessId") @NotBlank(message = "业务ID不能为空") String businessId,
                                   @RequestParam(value = "sendUser", required = false) String sendUser) {
        return emailPushService.sendQuoteEmail(quoteVersion, businessId, sendUser);
    }

    /**
     * 发送预估邮件
     *
     * @param estimateVersion 预估版本 (Z, ZZ)
     * @param businessId      业务ID
     * @param sendUser        发送人
     * @return 操作结果
     */
    @PostMapping("/sendEstimate")
    public R<String> sendEstimateEmail(@RequestParam("estimateVersion") @NotBlank(message = "预估版本不能为空") String estimateVersion,
                                      @RequestParam("businessId") @NotBlank(message = "业务ID不能为空") String businessId,
                                      @RequestParam(value = "sendUser", required = false) String sendUser) {
        return emailPushService.sendEstimateEmail(estimateVersion, businessId, sendUser);
    }

    /**
     * 发送P版邮件
     *
     * @param businessId 业务ID
     * @param sendUser   发送人
     * @return 操作结果
     */
    @PostMapping("/sendPVersion")
    public R<String> sendPVersionEmail(@RequestParam("businessId") @NotBlank(message = "业务ID不能为空") String businessId,
                                      @RequestParam(value = "sendUser", required = false) String sendUser) {
        return emailPushService.sendPVersionEmail(businessId, sendUser);
    }

    /**
     * 发送版本更新通知邮件
     *
     * @param businessId 业务ID
     * @param sendUser   发送人
     * @return 操作结果
     */
    @PostMapping("/sendUpdateNotify")
    public R<String> sendUpdateNotifyEmail(@RequestParam("businessId") @NotBlank(message = "业务ID不能为空") String businessId,
                                          @RequestParam(value = "sendUser", required = false) String sendUser) {
        return emailPushService.sendUpdateNotifyEmail(businessId, sendUser);
    }

    /**
     * 重试失败的邮件
     *
     * @param logId 日志ID
     * @return 操作结果
     */
    @PostMapping("/retry")
    public R<String> retryFailedEmail(@RequestParam("logId") @NotNull(message = "日志ID不能为空") Long logId) {
        return emailPushService.retryFailedEmail(logId);
    }

    /**
     * 批量重试失败的邮件
     *
     * @return 操作结果
     */
    @PostMapping("/batchRetry")
    public R<String> batchRetryFailedEmails() {
        return emailPushService.batchRetryFailedEmails();
    }

    /**
     * 分页查询邮件发送日志
     *
     * @param pageNo     页码
     * @param pageSize   页大小
     * @param emailType  邮件类型
     * @param sendStatus 发送状态
     * @param userNo     用户工号
     * @param businessId 业务ID
     * @param startDate  开始日期
     * @param endDate    结束日期
     * @return 分页结果
     */
    @GetMapping("/queryLogs")
    public R<PageInfo<EmailPushLog>> queryEmailLogs(
            @RequestParam(value = "pageNo", defaultValue = "1") int pageNo,
            @RequestParam(value = "pageSize", defaultValue = "10") int pageSize,
            @RequestParam(value = "emailType", required = false) String emailType,
            @RequestParam(value = "sendStatus", required = false) String sendStatus,
            @RequestParam(value = "userNo", required = false) String userNo,
            @RequestParam(value = "businessId", required = false) String businessId,
            @RequestParam(value = "startDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
            @RequestParam(value = "endDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate) {
        
        return emailPushService.queryEmailLogs(pageNo, pageSize, emailType, sendStatus, userNo, businessId, startDate, endDate);
    }

    /**
     * 根据批次ID查询邮件日志
     *
     * @param batchId 批次ID
     * @return 日志列表
     */
    @GetMapping("/getLogsByBatchId")
    public R<List<EmailPushLog>> getEmailLogsByBatchId(@RequestParam("batchId") @NotBlank(message = "批次ID不能为空") String batchId) {
        return emailPushService.getEmailLogsByBatchId(batchId);
    }

    /**
     * 根据业务ID查询邮件日志
     *
     * @param businessId 业务ID
     * @return 日志列表
     */
    @GetMapping("/getLogsByBusinessId")
    public R<List<EmailPushLog>> getEmailLogsByBusinessId(@RequestParam("businessId") @NotBlank(message = "业务ID不能为空") String businessId) {
        return emailPushService.getEmailLogsByBusinessId(businessId);
    }

    /**
     * 获取邮件发送统计信息
     *
     * @param emailType 邮件类型
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 统计结果
     */
    @GetMapping("/getStatistics")
    public R<List<EmailPushLog>> getEmailSendStatistics(
            @RequestParam(value = "emailType", required = false) String emailType,
            @RequestParam(value = "startDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
            @RequestParam(value = "endDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate) {
        
        return emailPushService.getEmailSendStatistics(emailType, startDate, endDate);
    }

    /**
     * 清理历史邮件日志
     *
     * @param beforeDate 指定日期之前的日志将被清理
     * @return 操作结果
     */
    @PostMapping("/cleanHistoryLogs")
    public R<String> cleanHistoryLogs(@RequestParam("beforeDate") @DateTimeFormat(pattern = "yyyy-MM-dd") Date beforeDate) {
        return emailPushService.cleanHistoryLogs(beforeDate);
    }

    /**
     * 测试邮件发送配置
     *
     * @param testEmail 测试邮箱
     * @return 操作结果
     */
    @PostMapping("/testConfig")
    public R<String> testEmailConfig(@RequestParam("testEmail") @NotBlank(message = "测试邮箱不能为空") String testEmail) {
        return emailPushService.testEmailConfig(testEmail);
    }
}
