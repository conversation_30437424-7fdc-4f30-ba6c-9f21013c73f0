package com.zqn.modeldata2.entity.email;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.Map;

/**
 * 邮件推送模板实体类
 * <AUTHOR> Assistant
 * @date 2025-01-22
 */
@Data
public class EmailPushTemplate {
    
    /** 模板ID */
    private String templateId;
    
    /** 模板名称 */
    private String templateName;
    
    /** 模板类型 */
    private String templateType;
    
    /** 主题模板 */
    private String subjectTemplate;
    
    /** 内容模板 */
    private String contentTemplate;
    
    /** 是否启用 */
    private String isActive;
    
    /** 创建用户 */
    private String createUser;
    
    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    
    /** 更新用户 */
    private String updateUser;
    
    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    
    /**
     * 根据参数渲染邮件主题
     * @param params 参数Map
     * @return 渲染后的主题
     */
    public String renderSubject(Map<String, Object> params) {
        return renderTemplate(subjectTemplate, params);
    }
    
    /**
     * 根据参数渲染邮件内容
     * @param params 参数Map
     * @return 渲染后的内容
     */
    public String renderContent(Map<String, Object> params) {
        return renderTemplate(contentTemplate, params);
    }
    
    /**
     * 模板渲染方法
     * @param template 模板字符串
     * @param params 参数Map
     * @return 渲染后的字符串
     */
    private String renderTemplate(String template, Map<String, Object> params) {
        if (template == null || params == null) {
            return template;
        }
        
        String result = template;
        for (Map.Entry<String, Object> entry : params.entrySet()) {
            String placeholder = "{" + entry.getKey() + "}";
            String value = entry.getValue() != null ? entry.getValue().toString() : "";
            result = result.replace(placeholder, value);
        }
        
        return result;
    }
    
    /**
     * 检查模板是否启用
     * @return 是否启用
     */
    public boolean isEnabled() {
        return "Y".equals(isActive);
    }
}
