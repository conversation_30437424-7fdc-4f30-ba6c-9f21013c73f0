<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zqn.modeldata2.mapper.EmailTemplateMapper">

    <resultMap id="ResultMap" type="com.zqn.modeldata2.entity.email.EmailTemplate">
        <result property="id" column="id" jdbcType="NUMERIC"/>
        <result property="templateCode" column="template_code" jdbcType="VARCHAR"/>
        <result property="templateName" column="template_name" jdbcType="VARCHAR"/>
        <result property="emailType" column="email_type" jdbcType="VARCHAR"/>
        <result property="subjectTemplate" column="subject_template" jdbcType="VARCHAR"/>
        <result property="contentTemplate" column="content_template" jdbcType="CLOB"/>
        <result property="variablesDesc" column="variables_desc" jdbcType="CLOB"/>
        <result property="language" column="language" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="CHAR"/>
        <result property="isDefault" column="is_default" jdbcType="CHAR"/>
        <result property="sortOrder" column="sort_order" jdbcType="NUMERIC"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
        <result property="createDate" column="create_date" jdbcType="DATE"/>
        <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
        <result property="updateDate" column="update_date" jdbcType="DATE"/>
    </resultMap>

    <!-- 根据ID查询邮件模板 -->
    <select id="selectById" resultMap="ResultMap">
        SELECT id, template_code, template_name, email_type, subject_template, content_template,
               variables_desc, language, status, is_default, sort_order, remark,
               create_user, create_date, update_user, update_date
        FROM pcc_email_template
        WHERE id = #{id}
    </select>

    <!-- 根据模板代码查询邮件模板 -->
    <select id="selectByTemplateCode" resultMap="ResultMap">
        SELECT id, template_code, template_name, email_type, subject_template, content_template,
               variables_desc, language, status, is_default, sort_order, remark,
               create_user, create_date, update_user, update_date
        FROM pcc_email_template
        WHERE template_code = #{templateCode}
          AND status = 'Y'
    </select>

    <!-- 根据邮件类型查询默认模板 -->
    <select id="selectDefaultByEmailType" resultMap="ResultMap">
        SELECT id, template_code, template_name, email_type, subject_template, content_template,
               variables_desc, language, status, is_default, sort_order, remark,
               create_user, create_date, update_user, update_date
        FROM pcc_email_template
        WHERE email_type = #{emailType}
          AND language = #{language}
          AND status = 'Y'
          AND is_default = 'Y'
        ORDER BY sort_order ASC
        FETCH FIRST 1 ROWS ONLY
    </select>

    <!-- 根据邮件类型查询所有模板 -->
    <select id="selectByEmailType" resultMap="ResultMap">
        SELECT id, template_code, template_name, email_type, subject_template, content_template,
               variables_desc, language, status, is_default, sort_order, remark,
               create_user, create_date, update_user, update_date
        FROM pcc_email_template
        WHERE email_type = #{emailType}
          AND status = 'Y'
        ORDER BY sort_order ASC, create_date DESC
    </select>

    <!-- 查询所有启用的模板 -->
    <select id="selectAllEnabled" resultMap="ResultMap">
        SELECT id, template_code, template_name, email_type, subject_template, content_template,
               variables_desc, language, status, is_default, sort_order, remark,
               create_user, create_date, update_user, update_date
        FROM pcc_email_template
        WHERE status = 'Y'
        ORDER BY email_type, sort_order ASC, create_date DESC
    </select>

    <!-- 根据条件查询模板列表 -->
    <select id="selectByCondition" resultMap="ResultMap">
        SELECT id, template_code, template_name, email_type, subject_template, content_template,
               variables_desc, language, status, is_default, sort_order, remark,
               create_user, create_date, update_user, update_date
        FROM pcc_email_template
        WHERE 1=1
        <if test="emailType != null and emailType != ''">
            AND email_type = #{emailType}
        </if>
        <if test="language != null and language != ''">
            AND language = #{language}
        </if>
        <if test="status != null and status != ''">
            AND status = #{status}
        </if>
        <if test="templateName != null and templateName != ''">
            AND template_name LIKE '%' || #{templateName} || '%'
        </if>
        ORDER BY email_type, sort_order ASC, create_date DESC
    </select>

    <!-- 插入邮件模板 -->
    <insert id="insert" parameterType="com.zqn.modeldata2.entity.email.EmailTemplate">
        INSERT INTO pcc_email_template (
            id, template_code, template_name, email_type, subject_template, content_template,
            variables_desc, language, status, is_default, sort_order, remark,
            create_user, create_date, update_user, update_date
        ) VALUES (
            seq_pcc_email_template.nextval, #{templateCode}, #{templateName}, #{emailType}, 
            #{subjectTemplate}, #{contentTemplate}, #{variablesDesc}, #{language}, 
            #{status}, #{isDefault}, #{sortOrder}, #{remark},
            #{createUser}, #{createDate}, #{updateUser}, #{updateDate}
        )
    </insert>

    <!-- 根据ID更新邮件模板 -->
    <update id="updateById" parameterType="com.zqn.modeldata2.entity.email.EmailTemplate">
        UPDATE pcc_email_template
        SET template_code = #{templateCode},
            template_name = #{templateName},
            email_type = #{emailType},
            subject_template = #{subjectTemplate},
            content_template = #{contentTemplate},
            variables_desc = #{variablesDesc},
            language = #{language},
            status = #{status},
            is_default = #{isDefault},
            sort_order = #{sortOrder},
            remark = #{remark},
            update_user = #{updateUser},
            update_date = #{updateDate}
        WHERE id = #{id}
    </update>

    <!-- 根据ID删除邮件模板 -->
    <delete id="deleteById">
        DELETE FROM pcc_email_template WHERE id = #{id}
    </delete>

    <!-- 根据模板代码删除邮件模板 -->
    <delete id="deleteByTemplateCode">
        DELETE FROM pcc_email_template WHERE template_code = #{templateCode}
    </delete>

    <!-- 检查模板代码是否存在 -->
    <select id="checkTemplateCodeExists" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM pcc_email_template
        WHERE template_code = #{templateCode}
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

    <!-- 设置默认模板 -->
    <update id="setDefaultTemplate">
        <!-- 先清除同类型的默认标识 -->
        UPDATE pcc_email_template 
        SET is_default = 'N', update_date = SYSDATE
        WHERE email_type = #{emailType} AND language = #{language};
        
        <!-- 再设置新的默认模板 -->
        UPDATE pcc_email_template 
        SET is_default = 'Y', update_date = SYSDATE
        WHERE id = #{id}
    </update>

    <!-- 清除指定邮件类型和语言的默认模板标识 -->
    <update id="clearDefaultTemplate">
        UPDATE pcc_email_template 
        SET is_default = 'N', update_date = SYSDATE
        WHERE email_type = #{emailType} AND language = #{language}
    </update>

    <!-- 根据条件统计模板数量 -->
    <select id="countByCondition" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM pcc_email_template
        WHERE 1=1
        <if test="emailType != null and emailType != ''">
            AND email_type = #{emailType}
        </if>
        <if test="language != null and language != ''">
            AND language = #{language}
        </if>
        <if test="status != null and status != ''">
            AND status = #{status}
        </if>
        <if test="templateName != null and templateName != ''">
            AND template_name LIKE '%' || #{templateName} || '%'
        </if>
    </select>

</mapper>
