package com.zqn.modeldata2.entity.email;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 邮件推送记录实体类
 * <AUTHOR> Assistant
 * @date 2025-01-22
 */
@Data
public class EmailPushRecord {
    
    /** 记录ID */
    private String recordId;
    
    /** 推送类型 */
    private String pushType;
    
    /** 关联业务数据ID */
    private String businessId;
    
    /** 业务类型 */
    private String businessType;
    
    /** 收件人用户工号 */
    private String recipientUserNo;
    
    /** 收件人邮箱 */
    private String recipientEmail;
    
    /** 收件人姓名 */
    private String recipientName;
    
    /** 收件人部门 */
    private String recipientDept;
    
    /** 邮件主题 */
    private String emailSubject;
    
    /** 邮件内容 */
    private String emailContent;
    
    /** 附件信息(JSON格式) */
    private String attachments;
    
    /** 发送状态 */
    private String sendStatus;
    
    /** 发送时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date sendTime;
    
    /** 错误信息 */
    private String errorMessage;
    
    /** 重试次数 */
    private Integer retryCount;
    
    /** 创建用户 */
    private String createUser;
    
    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    
    /**
     * 推送类型枚举
     */
    public enum PushType {
        QUOTE_V1("QUOTE_V1", "報價V1版本"),
        QUOTE_V2("QUOTE_V2", "報價V2版本"),
        QUOTE_V3("QUOTE_V3", "報價V3版本"),
        QUOTE_V4("QUOTE_V4", "報價V4版本"),
        QUOTE_V5("QUOTE_V5", "報價V5版本"),
        ESTIMATE_Z("ESTIMATE_Z", "預估Z版"),
        P_VERSION("P_VERSION", "P版"),
        VERSION_UPDATE("VERSION_UPDATE", "版本更新提醒");
        
        private final String code;
        private final String desc;
        
        PushType(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }
        
        public String getCode() {
            return code;
        }
        
        public String getDesc() {
            return desc;
        }
        
        public static PushType fromCode(String code) {
            for (PushType type : values()) {
                if (type.code.equals(code)) {
                    return type;
                }
            }
            return null;
        }
    }
    
    /**
     * 发送状态枚举
     */
    public enum SendStatus {
        PENDING("PENDING", "待发送"),
        SENDING("SENDING", "发送中"),
        SUCCESS("SUCCESS", "发送成功"),
        FAILED("FAILED", "发送失败");
        
        private final String code;
        private final String desc;
        
        SendStatus(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }
        
        public String getCode() {
            return code;
        }
        
        public String getDesc() {
            return desc;
        }
        
        public static SendStatus fromCode(String code) {
            for (SendStatus status : values()) {
                if (status.code.equals(code)) {
                    return status;
                }
            }
            return null;
        }
    }
    
    /**
     * 检查是否可以重试
     * @param maxRetryCount 最大重试次数
     * @return 是否可以重试
     */
    public boolean canRetry(int maxRetryCount) {
        return SendStatus.FAILED.getCode().equals(sendStatus) && 
               (retryCount == null || retryCount < maxRetryCount);
    }
    
    /**
     * 增加重试次数
     */
    public void incrementRetryCount() {
        if (retryCount == null) {
            retryCount = 1;
        } else {
            retryCount++;
        }
    }
}
