-- 邮件模板配置表
CREATE TABLE email_template (
    id NUMBER(10) PRIMARY KEY,                   -- 主键ID
    template_code VARCHAR2(50) NOT NULL UNIQUE, -- 模板编码
    template_name VARCHAR2(200) NOT NULL,       -- 模板名称
    template_type VARCHAR2(50) NOT NULL,        -- 模板类型 (对应推送类型)
    
    -- 模板内容
    subject_template VARCHAR2(500) NOT NULL,    -- 邮件主题模板
    content_template CLOB NOT NULL,             -- 邮件内容模板（支持HTML）
    
    -- 模板变量说明
    template_variables VARCHAR2(2000),          -- 模板变量说明（JSON格式）
    
    -- 配置信息
    is_html CHAR(1) DEFAULT 'Y',                -- 是否HTML格式 (Y-是, N-否)
    priority NUMBER(1) DEFAULT 3,               -- 邮件优先级 (1-高, 2-中, 3-低)
    need_attachment CHAR(1) DEFAULT 'N',        -- 是否需要附件 (Y-是, N-否)
    
    -- 状态控制
    status CHAR(1) DEFAULT 'Y',                 -- 状态 (Y-启用, N-禁用)
    remark VARCHAR2(500),                       -- 备注说明
    
    -- 审计字段
    create_user VARCHAR2(20),                   -- 创建人
    create_date DATE DEFAULT SYSDATE,          -- 创建时间
    update_user VARCHAR2(20),                  -- 更新人
    update_date DATE DEFAULT SYSDATE,          -- 更新时间
    
    -- 约束
    CONSTRAINT chk_template_is_html CHECK (is_html IN ('Y', 'N')),
    CONSTRAINT chk_template_priority CHECK (priority IN (1, 2, 3)),
    CONSTRAINT chk_template_need_attachment CHECK (need_attachment IN ('Y', 'N')),
    CONSTRAINT chk_template_status CHECK (status IN ('Y', 'N')),
    CONSTRAINT chk_template_type CHECK (template_type IN ('QUOTE_V1', 'QUOTE_V2', 'QUOTE_V3', 'QUOTE_V4', 'QUOTE_V5', 
                                                          'ESTIMATE_Z', 'ESTIMATE_ZZ', 'P_VERSION', 'UPDATE_NOTIFY'))
);

-- 创建索引
CREATE INDEX idx_email_template_code ON email_template(template_code);
CREATE INDEX idx_email_template_type ON email_template(template_type);
CREATE INDEX idx_email_template_status ON email_template(status);

-- 创建序列
CREATE SEQUENCE seq_email_template START WITH 1 INCREMENT BY 1;

-- 添加注释
COMMENT ON TABLE email_template IS '邮件模板配置表';
COMMENT ON COLUMN email_template.id IS '主键ID';
COMMENT ON COLUMN email_template.template_code IS '模板编码';
COMMENT ON COLUMN email_template.template_name IS '模板名称';
COMMENT ON COLUMN email_template.template_type IS '模板类型（对应推送类型）';
COMMENT ON COLUMN email_template.subject_template IS '邮件主题模板';
COMMENT ON COLUMN email_template.content_template IS '邮件内容模板（支持HTML）';
COMMENT ON COLUMN email_template.template_variables IS '模板变量说明（JSON格式）';
COMMENT ON COLUMN email_template.is_html IS '是否HTML格式 (Y-是, N-否)';
COMMENT ON COLUMN email_template.priority IS '邮件优先级 (1-高, 2-中, 3-低)';
COMMENT ON COLUMN email_template.need_attachment IS '是否需要附件 (Y-是, N-否)';
COMMENT ON COLUMN email_template.status IS '状态 (Y-启用, N-禁用)';
COMMENT ON COLUMN email_template.remark IS '备注说明';
COMMENT ON COLUMN email_template.create_user IS '创建人';
COMMENT ON COLUMN email_template.create_date IS '创建时间';
COMMENT ON COLUMN email_template.update_user IS '更新人';
COMMENT ON COLUMN email_template.update_date IS '更新时间';

-- 插入默认模板数据
INSERT INTO email_template (id, template_code, template_name, template_type, subject_template, content_template, template_variables, create_user) VALUES 
(seq_email_template.nextval, 'QUOTE_V1_TEMPLATE', '報價1版推送模板', 'QUOTE_V1', 
'【報價通知】${brandNo} - ${modelNo} 報價1版已更新', 
'<html><body><h3>報價1版更新通知</h3><p>品牌：${brandNo}</p><p>型体：${modelNo}</p><p>订单号：${ordNo}</p><p>更新时间：${updateTime}</p><p>请及时查看处理。</p></body></html>',
'{"brandNo":"品牌编号","modelNo":"型体编号","ordNo":"订单号","updateTime":"更新时间"}', 'SYSTEM');

INSERT INTO email_template (id, template_code, template_name, template_type, subject_template, content_template, template_variables, create_user) VALUES 
(seq_email_template.nextval, 'ESTIMATE_Z_TEMPLATE', '預估Z版推送模板', 'ESTIMATE_Z', 
'【預估通知】${brandNo} - ${modelNo} 預估Z版已更新', 
'<html><body><h3>預估Z版更新通知</h3><p>品牌：${brandNo}</p><p>型体：${modelNo}</p><p>订单号：${ordNo}</p><p>更新时间：${updateTime}</p><p>请及时查看处理。</p></body></html>',
'{"brandNo":"品牌编号","modelNo":"型体编号","ordNo":"订单号","updateTime":"更新时间"}', 'SYSTEM');

COMMIT;
