package com.zqn.modeldata2.controller;

import com.github.pagehelper.PageInfo;
import com.zqn.modeldata2.common.R;
import com.zqn.modeldata2.entity.email.EmailPermissionQuery;
import com.zqn.modeldata2.entity.email.EmailPushPermission;
import com.zqn.modeldata2.service.EmailPushPermissionService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 邮件推送权限配置Controller
 * @date 2025/01/22 10:00
 */
@RestController
@RequestMapping("/emailPushPermission")
@Validated
public class EmailPushPermissionController {

    @Resource
    private EmailPushPermissionService emailPushPermissionService;

    /**
     * 分页查询邮件推送权限列表
     *
     * @param pageNo       页码
     * @param pageSize     页大小
     * @param userNo       工号
     * @param deptName     部门名称
     * @param email        邮箱地址
     * @param emailType    邮件类型
     * @param status       状态
     * @return 分页结果
     */
    @GetMapping("/query")
    public R<PageInfo<EmailPushPermission>> queryPermissionList(
            @RequestParam(value = "pageNo", defaultValue = "1") int pageNo,
            @RequestParam(value = "pageSize", defaultValue = "10") int pageSize,
            @RequestParam(value = "userNo", required = false) String userNo,
            @RequestParam(value = "deptName", required = false) String deptName,
            @RequestParam(value = "email", required = false) String email,
            @RequestParam(value = "emailType", required = false) String emailType,
            @RequestParam(value = "status", required = false) String status,
            @RequestParam(value = "includeNoEmail", defaultValue = "N") String includeNoEmail) {
        
        EmailPermissionQuery query = new EmailPermissionQuery();
        query.setPageNo(pageNo);
        query.setPageSize(pageSize);
        query.setUserNo(userNo);
        query.setDeptName(deptName);
        query.setEmail(email);
        query.setEmailType(emailType);
        query.setStatus(status);
        query.setIncludeNoEmail(includeNoEmail);
        
        return emailPushPermissionService.queryPermissionList(query);
    }

    /**
     * 根据工号查询邮件推送权限
     *
     * @param userNo 工号
     * @return 权限信息
     */
    @GetMapping("/getByUserNo")
    public R<EmailPushPermission> getPermissionByUserNo(@RequestParam("userNo") @NotBlank(message = "工号不能为空") String userNo) {
        return emailPushPermissionService.getPermissionByUserNo(userNo);
    }

    /**
     * 根据邮件类型查询有权限的用户列表
     *
     * @param emailType 邮件类型
     * @return 权限列表
     */
    @GetMapping("/getByEmailType")
    public R<List<EmailPushPermission>> getPermissionsByEmailType(@RequestParam("emailType") @NotBlank(message = "邮件类型不能为空") String emailType) {
        return emailPushPermissionService.getPermissionsByEmailType(emailType);
    }

    /**
     * 根据邮件类型和部门查询有权限的用户列表
     *
     * @param emailType 邮件类型
     * @param deptName  部门名称
     * @return 权限列表
     */
    @GetMapping("/getByEmailTypeAndDept")
    public R<List<EmailPushPermission>> getPermissionsByEmailTypeAndDept(
            @RequestParam("emailType") @NotBlank(message = "邮件类型不能为空") String emailType,
            @RequestParam(value = "deptName", required = false) String deptName) {
        return emailPushPermissionService.getPermissionsByEmailTypeAndDept(emailType, deptName);
    }

    /**
     * 新增邮件推送权限
     *
     * @param permission 权限信息
     * @return 操作结果
     */
    @PostMapping("/add")
    public R<String> addPermission(@RequestBody @Validated EmailPushPermission permission) {
        return emailPushPermissionService.addPermission(permission);
    }

    /**
     * 更新邮件推送权限
     *
     * @param permission 权限信息
     * @return 操作结果
     */
    @PostMapping("/update")
    public R<String> updatePermission(@RequestBody @Validated EmailPushPermission permission) {
        return emailPushPermissionService.updatePermission(permission);
    }

    /**
     * 删除邮件推送权限
     *
     * @param userNo 工号
     * @return 操作结果
     */
    @PostMapping("/delete")
    public R<String> deletePermission(@RequestParam("userNo") @NotBlank(message = "工号不能为空") String userNo) {
        return emailPushPermissionService.deletePermission(userNo);
    }

    /**
     * 批量新增邮件推送权限
     *
     * @param permissions 权限列表
     * @return 操作结果
     */
    @PostMapping("/batchAdd")
    public R<String> batchAddPermissions(@RequestBody List<EmailPushPermission> permissions) {
        return emailPushPermissionService.batchAddPermissions(permissions);
    }

    /**
     * 批量更新邮件推送权限
     *
     * @param permissions 权限列表
     * @return 操作结果
     */
    @PostMapping("/batchUpdate")
    public R<String> batchUpdatePermissions(@RequestBody List<EmailPushPermission> permissions) {
        return emailPushPermissionService.batchUpdatePermissions(permissions);
    }

    /**
     * 根据用户工号列表查询权限信息
     *
     * @param userNos 工号列表
     * @return 权限列表
     */
    @PostMapping("/getByUserNos")
    public R<List<EmailPushPermission>> getPermissionsByUserNos(@RequestBody List<String> userNos) {
        return emailPushPermissionService.getPermissionsByUserNos(userNos);
    }

    /**
     * 查询所有部门列表
     *
     * @return 部门列表
     */
    @GetMapping("/getAllDepts")
    public R<List<String>> getAllDepts() {
        return emailPushPermissionService.getAllDepts();
    }

    /**
     * 检查用户是否有指定邮件类型的权限
     *
     * @param userNo    工号
     * @param emailType 邮件类型
     * @return 是否有权限
     */
    @GetMapping("/hasPermission")
    public R<Boolean> hasPermission(@RequestParam("userNo") @NotBlank(message = "工号不能为空") String userNo,
                                   @RequestParam("emailType") @NotBlank(message = "邮件类型不能为空") String emailType) {
        boolean hasPermission = emailPushPermissionService.hasPermission(userNo, emailType);
        return R.success(hasPermission);
    }

    /**
     * 同步用户权限
     *
     * @return 操作结果
     */
    @PostMapping("/syncUserPermissions")
    public R<String> syncUserPermissions() {
        return emailPushPermissionService.syncUserPermissions();
    }
}
