package com.zqn.modeldata2.mapper;

import com.zqn.modeldata2.entity.email.EmailPushConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 邮件推送配置Mapper接口
 * @date 2025/01/22 10:00
 */
@Mapper
public interface EmailPushConfigMapper {

    /**
     * 根据ID查询配置
     *
     * @param id 配置ID
     * @return 配置信息
     */
    EmailPushConfig selectById(@Param("id") Long id);

    /**
     * 根据配置键查询配置
     *
     * @param configKey 配置键
     * @return 配置信息
     */
    EmailPushConfig selectByConfigKey(@Param("configKey") String configKey);

    /**
     * 根据配置类型查询配置列表
     *
     * @param configType 配置类型
     * @return 配置列表
     */
    List<EmailPushConfig> selectByConfigType(@Param("configType") String configType);

    /**
     * 查询所有启用的配置
     *
     * @return 配置列表
     */
    List<EmailPushConfig> selectAllEnabled();

    /**
     * 根据条件查询配置列表
     *
     * @param configKey  配置键（模糊查询）
     * @param configType 配置类型
     * @param status     状态
     * @return 配置列表
     */
    List<EmailPushConfig> selectByCondition(@Param("configKey") String configKey,
                                           @Param("configType") String configType,
                                           @Param("status") String status);

    /**
     * 插入配置
     *
     * @param config 配置信息
     * @return 影响行数
     */
    int insert(EmailPushConfig config);

    /**
     * 根据ID更新配置
     *
     * @param config 配置信息
     * @return 影响行数
     */
    int updateById(EmailPushConfig config);

    /**
     * 根据配置键更新配置值
     *
     * @param configKey   配置键
     * @param configValue 配置值
     * @param updateUser  更新人
     * @return 影响行数
     */
    int updateValueByKey(@Param("configKey") String configKey,
                        @Param("configValue") String configValue,
                        @Param("updateUser") String updateUser);

    /**
     * 根据ID删除配置
     *
     * @param id 配置ID
     * @return 影响行数
     */
    int deleteById(@Param("id") Long id);

    /**
     * 根据配置键删除配置
     *
     * @param configKey 配置键
     * @return 影响行数
     */
    int deleteByConfigKey(@Param("configKey") String configKey);

    /**
     * 检查配置键是否存在
     *
     * @param configKey 配置键
     * @param excludeId 排除的ID（用于更新时检查）
     * @return 数量
     */
    int checkConfigKeyExists(@Param("configKey") String configKey, 
                            @Param("excludeId") Long excludeId);

    /**
     * 批量更新配置
     *
     * @param configs 配置列表
     * @return 影响行数
     */
    int batchUpdate(@Param("configs") List<EmailPushConfig> configs);

    /**
     * 根据条件统计配置数量
     *
     * @param configKey  配置键（模糊查询）
     * @param configType 配置类型
     * @param status     状态
     * @return 数量
     */
    int countByCondition(@Param("configKey") String configKey,
                        @Param("configType") String configType,
                        @Param("status") String status);
}
